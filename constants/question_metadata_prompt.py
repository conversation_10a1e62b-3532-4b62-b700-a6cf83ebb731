# Question metadata template
# QUESTION_METADATA_PROMPT = """
# Consider this context to get the description of the entity and give response by considering this context={entity_context}.
# This conversation is for module={module_identifier},entity_type={entity_type}, entity_id={entity_id} and entity_name={entity_name}.
# For further similar questions asked by user in this conversation, if there is no context is provided, please retain and use the same context for them.
# """

QUESTION_METADATA_PROMPT = """Consider this description of the entity based on the following context: {entity_context}.
This request is related to module: {module_identifier}, entity type: {entity_type}, ID: {entity_id}, and name: {entity_name}.
For any subsequent questions in this conversation without explicit context, please refer back to the context provided here."""