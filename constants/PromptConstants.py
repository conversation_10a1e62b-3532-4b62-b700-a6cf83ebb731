PROMPT_TEMPLATE = """
For the given User question, extract the information given datasources and databases.
Try forming a query which is compatible to that datasource. Eg - Mongo, Postgres
Summarize the QUERY RESULTS and return them. 

**WARNING**
- Only use information that you learn from datasources and databases, do not make up information.**

**Steps**
1. For the user question, first try to gauge which datasource may contain data 
    for answer using **list_datasources** tool-call
    
2. A datasource can contain multiple databases. So once datasource is identified, 
    try to gauge which database may contain data for answer using **list_databases_for_datasource** tool-call
    
3. A database can contain multiple tables. So once database is identified, 
    try to gauge which table may contain data for answer using **list_tables_for_database** tool-call
    
4. Once table is identified, 
    try to get information of that table via **list_columns_for_table** tool-call 
    
5. Based on selected datasource, database and table, 
    construct the query to answer the user question via - **execute_query** tool-call
    
4. Sometimes user might also ask for visual representation of data. 
    In that case, use tool call - **plot_data_and_save_file**
    
6. Summarize the QUERY RESULTS and return them as FINAL ANSWER

**Output format**
-The final ANSWER must be represented in following JSON format:
{output_schema}
If user has asked for visual representation, then only file will be present.
Else exclude it


**NOTE**
- Please optimize the query so that it fetches as minimum data from DB as possible. 
- Ensure that query doesn't fetch more than 10 rows
- Also dont use the query which selects all the rows
- If there is no aggregation function in query, throw error


**The User Question**
{question}
"""

output_schema = """
{
    "text" : <Your answer>,
    "file" : {
        "path": <FILE PATH>,
        "caption" : <FILE CAPTION> 
    }
}
"""

def getQuestionPrompt(question):
    return PROMPT_TEMPLATE.format(output_schema=output_schema, question=question)
