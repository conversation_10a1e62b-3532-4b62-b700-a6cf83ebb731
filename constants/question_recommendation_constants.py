# Thresholds for question recommendation generation
CONVERSATION_HISTORY_THRESHOLD = 5  ## Minimum questions required for current conversation
MINIMUM_USER_HISTORY_THRESHOLD = 5 ## Minimum questions required across all conversations
MAXIMUM_USER_HISTORY_THRESHOLD = 20 ## Maximum questions required across all conversations

# AI Question Recommendation Configurations
AI_QUESTION_RECOMMENDATION_MODEL = "gpt-4o-mini"
AI_QUESTION_RECOMMENDATION_COUNT = 3

AI_RESPONSE_TEMPLATE = """
Based on the user's previous questions for module = {module_identifier} and entity = {entity_type}, please recommend the next {num_recommendations} questions.

Primary criteria:
1. First, prioritize recommendations that are most relevant to the entity type "{entity_type}".
2. If sufficient recommendations cannot be generated for the entity type, expand the scope to include recommendations for the module "{module_identifier}" as a fallback.

The user questions are:
{user_questions}

Please ensure there are no empty recommendations. If recommendations for the entity type are unavailable, clearly note that the suggestions are based on the module instead.

Respond in the following format:
{{
    "recommended_questions": [ LIST of recommended questions for the entity/module ]
}}
"""