#!/usr/bin/env python3
"""
Debug script to test question_query_history table functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from config.app_config import Config
from models.meta_data import db
from models.conversation_meta_data import QuestionQueryHistory
from dal.question_query_history_dal import create_question_query_entry, get_all_question_query_pairs

def create_test_app():
    """Create a test Flask app with database configuration"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def test_manual_insertion():
    """Test manual insertion of your questions"""
    print("🧪 Testing manual insertion of your questions...")
    
    test_questions = [
        {
            "question": "How many vessels are in the fleet?",
            "queries": ["SELECT COUNT(*) FROM vessels", "SELECT vessel_name FROM vessels"]
        },
        {
            "question": "Show me vessel names and IMO numbers",
            "queries": ["SELECT vessel_name, imo_number FROM vessels"]
        },
        {
            "question": "What is the total capacity of vessels?",
            "queries": ["SELECT SUM(capacity) FROM vessels", "SELECT vessel_name, capacity FROM vessels"]
        },
        {
            "question": "What is 5+9?",
            "queries": ["-- This is a math question, no SQL needed"]
        }
    ]
    
    for i, test_case in enumerate(test_questions):
        try:
            create_question_query_entry(test_case["question"], test_case["queries"])
            print(f"✅ Question {i+1} inserted successfully: {test_case['question']}")
        except Exception as e:
            print(f"❌ Question {i+1} failed: {e}")

def check_current_state():
    """Check current state of question_query_history table"""
    print("\n📊 Current state of question_query_history table:")
    
    try:
        all_entries = get_all_question_query_pairs(limit=20)
        print(f"📈 Total entries: {len(all_entries)}")
        
        if not all_entries:
            print("❌ No entries found in question_query_history table!")
            return
            
        for i, entry in enumerate(all_entries):
            print(f"\n  {i+1}. ID: {entry.id}")
            print(f"     Question: {entry.question_text}")
            print(f"     Queries: {entry.queries}")
            print(f"     Created: {entry.created_at}")
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function"""
    print("🚀 Debugging question_query_history table...")
    print("=" * 60)
    
    app = create_test_app()
    
    with app.app_context():
        try:
            # Create tables if they don't exist
            db.create_all()
            print("📋 Database tables created/verified")
            
            # Check current state first
            check_current_state()
            
            # Ask if user wants to insert test data
            choice = input("\n🤔 Do you want to insert test questions? (y/n): ").lower().strip()
            if choice == 'y':
                test_manual_insertion()
                print("\n📊 After insertion:")
                check_current_state()
            
            print("\n✅ Debug completed!")
            
        except Exception as e:
            print(f"❌ Debug failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
