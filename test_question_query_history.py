#!/usr/bin/env python3
"""
Test script for question_query_history table insertion and QuestionQueryManager functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from config.app_config import Config
from models.meta_data import db
from models.conversation_meta_data import QuestionQueryHistory
from dal.question_query_history_dal import create_question_query_entry, QuestionQueryManager, get_queries_by_question, get_all_question_query_pairs

def create_test_app():
    """Create a test Flask app with database configuration"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def test_basic_insertion():
    """Test basic insertion into question_query_history table"""
    print("🧪 Testing basic insertion...")
    
    test_question = "How many vessels are there in this account?"
    test_queries = [
        "SELECT COUNT(*) FROM vessels WHERE account_id = 'current_account'",
        "SELECT vessel_id, vessel_name FROM vessels WHERE account_id = 'current_account'"
    ]
    
    try:
        # Test direct insertion
        create_question_query_entry(test_question, test_queries)
        print("✅ Basic insertion successful!")
        
        # Verify insertion
        retrieved_queries = get_queries_by_question(test_question)
        print(f"📋 Retrieved queries: {retrieved_queries}")
        
        if retrieved_queries == test_queries:
            print("✅ Query retrieval successful!")
        else:
            print("❌ Query retrieval mismatch!")
            
    except Exception as e:
        print(f"❌ Basic insertion failed: {e}")

def test_question_query_manager():
    """Test QuestionQueryManager functionality"""
    print("\n🧪 Testing QuestionQueryManager...")
    
    manager = QuestionQueryManager()
    
    # Test data
    test_cases = [
        {
            "question": "How many ships are in the fleet?",
            "queries": ["SELECT COUNT(*) FROM ships", "SELECT ship_name FROM ships"]
        },
        {
            "question": "What is the total cargo capacity?",
            "queries": ["SELECT SUM(cargo_capacity) FROM vessels", "SELECT vessel_id, cargo_capacity FROM vessels"]
        },
        {
            "question": "How many vessels are there?",  # Similar to first question
            "queries": ["SELECT COUNT(*) FROM vessels"]
        }
    ]
    
    # Test upsert functionality
    for i, test_case in enumerate(test_cases):
        try:
            result = manager.upsert_question_with_queries(test_case["question"], test_case["queries"])
            print(f"✅ Test case {i+1} - Upsert result: {result}")
        except Exception as e:
            print(f"❌ Test case {i+1} - Upsert failed: {e}")
    
    # Test similarity search
    print("\n🔍 Testing similarity search...")
    search_question = "How many vessels are in this account?"
    
    try:
        similar_queries, similar_question = manager.get_queries_for_question(search_question)
        print(f"🎯 Search question: {search_question}")
        print(f"📝 Similar question found: {similar_question}")
        print(f"📋 Similar queries: {similar_queries}")
        
        if similar_queries:
            print("✅ Similarity search successful!")
        else:
            print("⚠️ No similar questions found (this might be expected)")
            
    except Exception as e:
        print(f"❌ Similarity search failed: {e}")

def test_database_state():
    """Check current state of question_query_history table"""
    print("\n📊 Checking database state...")
    
    try:
        all_entries = get_all_question_query_pairs(limit=10)
        print(f"📈 Total entries in database: {len(all_entries)}")
        
        for i, entry in enumerate(all_entries):
            print(f"  {i+1}. Question: {entry.question_text}")
            print(f"     Queries: {len(entry.queries)} queries")
            print(f"     Created: {entry.created_at}")
            print()
            
    except Exception as e:
        print(f"❌ Database state check failed: {e}")

def cleanup_test_data():
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    
    test_questions = [
        "How many vessels are there in this account?",
        "how many ships are in the fleet?",  # lowercase as stored
        "what is the total cargo capacity?",
        "how many vessels are there?"
    ]
    
    manager = QuestionQueryManager()
    
    for question in test_questions:
        try:
            deleted = manager.delete_question(question)
            if deleted:
                print(f"🗑️ Deleted: {question}")
            else:
                print(f"⚠️ Not found: {question}")
        except Exception as e:
            print(f"❌ Failed to delete {question}: {e}")

def main():
    """Main test function"""
    print("🚀 Starting question_query_history table tests...")
    print("=" * 60)
    
    # Create Flask app context
    app = create_test_app()
    
    with app.app_context():
        try:
            # Create tables if they don't exist
            db.create_all()
            print("📋 Database tables created/verified")
            
            # Run tests
            test_basic_insertion()
            test_question_query_manager()
            test_database_state()
            
            # Ask user if they want to cleanup
            cleanup_choice = input("\n🤔 Do you want to clean up test data? (y/n): ").lower().strip()
            if cleanup_choice == 'y':
                cleanup_test_data()
            
            print("\n✅ All tests completed!")
            
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
