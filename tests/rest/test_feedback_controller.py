import pytest
from unittest.mock import patch
from utils.PaginationUtils import Pageable, SortOrder


class MockFeedback:
    def __init__(self, id, user_id, tenant_name, conversation_id, sentiment, comment, question, answer):
        self.id = id
        self.user_id = user_id
        self.tenant_name = tenant_name
        self.conversation_id = conversation_id
        self.sentiment = sentiment
        self.comment = comment
        self.question = question
        self.answer = answer


@pytest.fixture
def mock_feedback():
    return MockFeedback(
        id="123e4567-e89b-12d3-a456-426614174000",
        user_id="user-001",
        tenant_name="org-001",
        conversation_id="conv-001",
        sentiment="POSITIVE",
        comment=None,
        question="What is AI?",
        answer="Artificial Intelligence"
    )


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
@patch('service.feedback_service.find_feedbacks_by_user_and_thread')
def test_get_feedbacks_by_conversation(mock_find_feedbacks_by_user_and_thread, mock_get_user_and_tenant, client,
                                       mock_feedback):
    mock_get_user_and_tenant.return_value = ("user-001", "org-001")

    mock_feedbacks = [mock_feedback]
    mock_find_feedbacks_by_user_and_thread.return_value = mock_feedbacks

    response = client.get(
        '/api/v1/conversations/conv-001/feedbacks',
        headers={
            'User-Id': 'user-001',
            'Tenant-Name': 'org-001'
        }
    )

    assert response.status_code == 200
    assert len(response.json) == 1
    assert response.json[0]['id'] == "123e4567-e89b-12d3-a456-426614174000"
    mock_find_feedbacks_by_user_and_thread.assert_called_once_with(
        "user-001", "org-001", "conv-001"
    )


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
@patch('service.feedback_service.find_feedbacks_by_filters')
def test_get_feedbacks_empty(mock_find_feedbacks_by_filters, mock_get_user_and_tenant, client):
    mock_get_user_and_tenant.return_value = ("user-001", "org-001")

    mock_find_feedbacks_by_filters.return_value = ([], 0)

    response = client.get('/api/v1/feedbacks')

    assert response.status_code == 200
    assert response.json['total'] == 0
    assert len(response.json['data']) == 0

    filters = {}
    page_number = 1
    page_size = 10
    sort_by = 'createdAt'
    sort_order = 'DESC'

    pageable = Pageable(
        page_number=page_number,
        page_size=page_size,
        sort_by=sort_by,
        sort_order=SortOrder[sort_order]
    )

    mock_find_feedbacks_by_filters.assert_called_once_with(filters, pageable)
