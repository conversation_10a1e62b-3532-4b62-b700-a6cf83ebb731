from unittest.mock import patch


@patch('service.question_recommendation_service.get_recommended_questions_for_chat')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_get_question_recommendations_success(mock_get_user_and_tenant, mock_get_recommended_questions_for_chat,
                                              client):
    mock_get_user_and_tenant.return_value = ("user-001", "tenant-001")

    mock_get_recommended_questions_for_chat.return_value = [
        "What is the definition of AI?",
        "How does machine learning work?",
        "What are neural networks?"
    ]

    response = client.get(
        '/api/v1/question-recommendations',
        headers={
            'User-Id': 'user-001',
            'Tenant-Name': 'tenant-001'
        },
        query_string={'module_identifier': 'event-timeline'}
    )

    assert response.status_code == 200
    assert "recommended_questions" in response.json
    assert len(response.json["recommended_questions"]) == 3
    assert response.json["recommended_questions"][0] == "What is the definition of AI?"
    mock_get_recommended_questions_for_chat.assert_called_once_with(
        "user-001", "event-timeline", None, None
    )


@patch('service.question_recommendation_service.get_recommended_questions_for_chat')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_get_question_recommendations_with_conversation_id(mock_get_user_and_tenant,
                                                           mock_get_recommended_questions_for_chat, client):
    mock_get_user_and_tenant.return_value = ("user-001", "tenant-001")

    mock_get_recommended_questions_for_chat.return_value = [
        "What is the definition of AI?",
        "How does machine learning work?"
    ]

    response = client.get(
        '/api/v1/question-recommendations',
        headers={
            'User-Id': 'user-001',
            'Tenant-Name': 'tenant-001'
        },
        query_string={'module_identifier': 'event-timeline', 'conversation_id': 'conv-001'}
    )

    assert response.status_code == 200
    assert "recommended_questions" in response.json
    assert len(response.json["recommended_questions"]) == 2
    assert response.json["recommended_questions"][0] == "What is the definition of AI?"
    mock_get_recommended_questions_for_chat.assert_called_once_with(
        "user-001", "event-timeline", "conv-001", None
    )
