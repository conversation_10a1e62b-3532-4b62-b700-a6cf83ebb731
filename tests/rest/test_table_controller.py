from unittest.mock import patch, MagicMock
from flask.testing import FlaskClient
import pytest


class MockTableMetaData:
    def __init__(self, name, description, trino_db_name):
        self.name = name
        self.description = description
        self.trino_db_name = trino_db_name

    def as_dict(self):
        return {"name": self.name, "description": self.description, "trino_db_name": self.trino_db_name}


@pytest.fixture
def mock_table_metadata():
    return [
        MockTableMetaData(name="table1", description="Table 1", trino_db_name="test_db"),
        MockTableMetaData(name="table2", description="Table 2", trino_db_name="test_db"),
    ]


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "tenant1"))
@patch('service.table_service.find_by_db_name')
def test_get_tables_by_database(mock_find_by_db_name, mock_get_user_and_tenant, client: FlaskClient,
                                mock_table_metadata):
    mock_find_by_db_name.return_value = [table.as_dict() for table in mock_table_metadata]

    response = client.get('/api/v1/databases/test_db/tables', headers={"Tenant-Name": "tenant1"})

    assert response.status_code == 200
    assert len(response.json) == 2
    assert response.json[0]["name"] == "table1"
    assert response.json[1]["name"] == "table2"
    mock_find_by_db_name.assert_called_once_with("test_db", "tenant1")


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "tenant1"))
@patch('service.table_service.find_all')
def test_get_tables(mock_find_all, mock_get_user_and_tenant, client: FlaskClient, mock_table_metadata):
    mock_find_all.return_value = [table.as_dict() for table in mock_table_metadata]

    response = client.get('/api/v1/tables', headers={"Tenant-Name": "tenant1"})

    assert response.status_code == 200
    assert len(response.json) == 2
    assert response.json[0]["name"] == "table1"
    assert response.json[1]["name"] == "table2"
    mock_find_all.assert_called_once_with("tenant1")


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "tenant1"))
@patch('service.table_service.find_by_table_name')
@patch('service.table_service.update')
def test_update_table(mock_update, mock_find, mock_get_user_and_tenant, client: FlaskClient):
    existing_table = MockTableMetaData(name="table1", description="Table 1", trino_db_name="db1")
    mock_find.return_value = existing_table
    updated_table = MockTableMetaData(name="table1", description="Updated Table", trino_db_name="db1")
    mock_update.return_value = updated_table

    response = client.put('/api/v1/tables/table1', json={"description": "Updated Table"},
                          headers={"Tenant-Name": "tenant1"})

    assert response.status_code == 200
    assert response.json["description"] == "Updated Table"
    mock_find.assert_called_once_with("table1", "tenant1")
    mock_update.assert_called_once_with(existing_table)


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "tenant1"))
@patch('service.table_service.find_by_table_name')
@patch('service.column_service.find_by_table_name')
@patch('service.column_service.delete')
@patch('service.table_service.delete')
def test_delete_table(mock_delete_table, mock_delete_column, mock_find_columns, mock_find_table,
                      mock_get_user_and_tenant, client: FlaskClient):
    existing_table = MockTableMetaData(name="table1", description="Table 1", trino_db_name="db1")
    mock_find_table.return_value = existing_table
    mock_find_columns.return_value = [MockTableMetaData(name="column1", description="Column 1", trino_db_name="db1")]

    response = client.delete('/api/v1/tables/table1', headers={"Tenant-Name": "tenant1"})

    assert response.status_code == 200
    assert response.json["message"] == "TableMetaData deleted successfully"
    mock_find_table.assert_called_once_with("table1", "tenant1")
    mock_find_columns.assert_called_once_with("table1", "tenant1")
    mock_delete_column.assert_called_once()
    mock_delete_table.assert_called_once_with(existing_table)
