from unittest.mock import patch
from interceptor.thread_context import ThreadLocalContext


class MockDBMetaData:
    def __init__(self, name, description, trino_catalog_name):
        self.name = name
        self.description = description
        self.trino_catalog_name = trino_catalog_name

    def to_dict(self):
        return {
            "name": self.name,
            "description": self.description,
            "trino_catalog_name": self.trino_catalog_name,
        }


def mock_thread_local_context():
    ThreadLocalContext.set_user_and_tenant(user_id="test_user", tenant_name="tenant1")


@patch('service.db_service.find_by_catalog_name')
def test_get_databases_by_catalog(mock_find, client):
    mock_thread_local_context()

    mock_find.return_value = [
        MockDBMetaData(name="db1", description="Database 1", trino_catalog_name="catalog1"),
        MockDBMetaData(name="db2", description="Database 2", trino_catalog_name="catalog1")
    ]

    response = client.get('/api/v1/catalogs/test_catalog/databases',
                          headers={"Tenant-Name": "tenant1", "User-Id": "test_user"})

    assert response.status_code == 200
    assert len(response.json) == 2
    assert response.json[0]['name'] == "db1"
    mock_find.assert_called_once_with("test_catalog", "tenant1")


@patch('service.db_service.find_by_db_name')
def test_get_database_by_name(mock_find, client):
    mock_thread_local_context()

    mock_find.return_value = MockDBMetaData(name="db1", description="Database 1", trino_catalog_name="catalog1")

    response = client.get('/api/v1/databases/db1', headers={"Tenant-Name": "tenant1", "User-Id": "test_user"})

    assert response.status_code == 200
    assert response.json['name'] == "db1"
    mock_find.assert_called_once_with("db1", "tenant1")


@patch('service.db_service.find_all')
def test_get_databases(mock_find_all, client):
    mock_thread_local_context()

    mock_find_all.return_value = [
        MockDBMetaData(name="db1", description="Database 1", trino_catalog_name="catalog1"),
        MockDBMetaData(name="db2", description="Database 2", trino_catalog_name="catalog1")
    ]

    response = client.get('/api/v1/databases', headers={"Tenant-Name": "tenant1", "User-Id": "test_user"})

    assert response.status_code == 200
    assert len(response.json) == 2
    assert response.json[0]['name'] == "db1"
    mock_find_all.assert_called_once_with("tenant1")


@patch('service.db_service.find_by_db_name')
@patch('service.db_service.update')
def test_update_database(mock_update, mock_find, client):
    mock_thread_local_context()

    existing_db = MockDBMetaData(name="db1", description="Database 1", trino_catalog_name="catalog1")
    mock_find.return_value = existing_db
    updated_db = MockDBMetaData(name="db1", description="Updated Database", trino_catalog_name="catalog1")
    mock_update.return_value = updated_db

    response = client.put('/api/v1/databases/db1',
                          json={"description": "Updated Database"},
                          headers={"Tenant-Name": "tenant1", "User-Id": "test_user"})

    assert response.status_code == 200
    assert response.json['description'] == "Updated Database"
    mock_find.assert_called_once_with("db1", "tenant1")
    mock_update.assert_called_once_with(existing_db)
