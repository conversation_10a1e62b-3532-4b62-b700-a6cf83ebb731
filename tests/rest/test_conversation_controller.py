from unittest import mock
from unittest.mock import patch, ANY


class MockConversation:
    def __init__(self, id, user_id, tenant_name, first_message):
        self.id = id
        self.user_id = user_id
        self.tenant_name = tenant_name
        self.first_message = first_message


class MockResponse:
    def __init__(self, text, file=None):
        self.text = text
        self.file = file


@patch('service.conversation_service.answer_question')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_answer_question(mock_get_user_and_tenant, mock_answer, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_answer.return_value = MockResponse(
        text="This is the response"
    )

    response = client.post('/api/v1/conversations/1/messages', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    }, json={
        "question": "What is the weather today?"
    })

    assert response.status_code == 200
    assert response.json['text'] == "This is the response"
    assert response.json['file'] is None
    mock_answer.assert_called_once_with("user1", "1", "tenant1", "What is the weather today?")
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.find_by_user_and_tenant')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_get_conversations(mock_get_user_and_tenant, mock_find, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_find.return_value = (
        [MockConversation(
            id="1",
            user_id="user1",
            tenant_name="tenant1",
            first_message="Hello"
        )],
        1
    )

    response = client.get('/api/v1/conversations', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    })

    assert response.status_code == 200
    assert len(response.json['data']) == 1
    assert response.json['total'] == 1
    assert response.json['data'][0]['id'] == "1"
    assert response.json['data'][0]['user_id'] == "user1"
    mock_find.assert_called_once()
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.get_messages')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_get_messages(mock_get_user_and_tenant, mock_get_messages, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_get_messages.return_value = (
        [
            {"role": "assistant", "text": "Hello, how can I help you?", "timestamp": 1692014400}
        ],
        1
    )

    response = client.get('/api/v1/conversations/1/messages', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    })

    assert response.status_code == 200
    assert len(response.json['data']) == 1
    assert response.json['total'] == 1
    assert response.json['data'][0]['text'] == "Hello, how can I help you?"
    assert response.json['data'][0]['role'] == "assistant"
    mock_get_messages.assert_called_once_with("user1", "tenant1", "1", mock.ANY)
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.delete_conversation')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_delete_conversation(mock_get_user_and_tenant, mock_delete, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")

    response = client.delete('/api/v1/conversations/1', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    })

    assert response.status_code == 200
    assert response.json['message'] == "Conversation deleted successfully"
    mock_delete.assert_called_once_with("user1", "tenant1", "1")
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.get_recent_conversation')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_get_recent_conversation(mock_get_user_and_tenant, mock_get_recent, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_get_recent.return_value = MockConversation(
        id="1",
        user_id="user1",
        tenant_name="tenant1",
        first_message="Hello"
    )

    response = client.get('/api/v1/conversations/recent', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    })

    assert response.status_code == 200
    assert response.json['id'] == "1"
    assert response.json['user_id'] == "user1"
    assert response.json['tenant_name'] == "tenant1"
    mock_get_recent.assert_called_once_with("user1", "tenant1")
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.find_by_user_and_tenant')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_get_conversations_with_pagination(mock_get_user_and_tenant, mock_find, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_find.return_value = (
        [{"id": "1", "user_id": "user1", "tenant_name": "tenant1", "first_message": "Hello"}], 1
    )

    response = client.get('/api/v1/conversations?pageNumber=2&pageSize=10', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    })

    assert response.status_code == 200
    assert len(response.json['data']) == 1
    assert response.json['total'] == 1
    mock_find.assert_called_once()
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.clear_conversation')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_clear_conversations_with_excluded_ids(mock_get_user_and_tenant, mock_clear, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")

    response = client.delete('/api/v1/conversations?excluded_ids=1,2', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    })

    assert response.status_code == 200
    assert "Cleared previous conversation successfully" in response.json['message']
    mock_clear.assert_called_once_with("user1", "tenant1", ["1", "2"])
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.answer_question_stream')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_answer_question_stream(mock_get_user_and_tenant, mock_answer_stream, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_answer_stream.return_value = None

    response = client.post('/api/v1/conversations/1/messages/stream', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    }, json={
        "question": "What is the weather today?"
    })

    assert response.status_code == 200
    assert "text/event-stream" in response.headers['Content-Type']
    mock_answer_stream.assert_called_once()
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.get_messages')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_get_messages_with_pagination(mock_get_user_and_tenant, mock_get_messages, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_get_messages.return_value = (
        [{"role": "assistant", "text": "Hello, how can I help you?", "timestamp": 1692014400}], 1
    )

    response = client.get('/api/v1/conversations/1/messages?pageNumber=2&pageSize=10', headers={
        "User-Id": "user1",
        "Tenant-Name": "tenant1"
    })

    assert response.status_code == 200
    assert len(response.json['data']) == 1
    assert response.json['total'] == 1
    mock_get_messages.assert_called_once_with("user1", "tenant1", "1", ANY)
    mock_get_user_and_tenant.assert_called_once()


@patch('service.conversation_service.create_new_conversation')
@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant')
def test_start_conversation_with_all_optional_params(mock_get_user_and_tenant, mock_create, client):
    mock_get_user_and_tenant.return_value = ("user1", "tenant1")
    mock_create.return_value = {
        "id": "1",
        "user_id": "user1",
        "tenant_name": "tenant1",
        "first_message": "Hello"
    }

    response = client.post(
        '/api/v1/conversations?module_identifier=event-timeline&entity_type=vessel&entity_id=IMO123&entity_name=ExampleVessel',
        headers={
            "User-Id": "user1",
            "Tenant-Name": "tenant1"
        })

    assert response.status_code == 201
    assert response.json['id'] == "1"
    assert response.json['user_id'] == "user1"
    mock_create.assert_called_once_with("user1", "tenant1", "event-timeline", "vessel", "IMO123", "ExampleVessel")
    mock_get_user_and_tenant.assert_called_once()
