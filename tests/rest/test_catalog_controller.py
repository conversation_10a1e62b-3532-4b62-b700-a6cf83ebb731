from unittest.mock import patch
import pytest
from constants.app_constants import USER_ID_HEADER, TENANT_NAME_HEADER
from interceptor.interceptor import before_request


class MockCatalogMetaData:
    def __init__(self, name, description, tenant_name):
        self.name = name
        self.description = description
        self.tenant_name = tenant_name

    def to_dict(self):
        return {
            "name": self.name,
            "description": self.description,
            "tenant_name": self.tenant_name,
        }


@pytest.fixture
def tenant_name():
    return "test_tenant"


@pytest.fixture
def user_id():
    return "test_user"


@pytest.fixture
def request_headers(tenant_name, user_id):
    return {
        USER_ID_HEADER: user_id,
        TENANT_NAME_HEADER: tenant_name,
    }


@pytest.fixture
def setup_request_context(client, request_headers):
    with client.application.test_request_context(headers=request_headers):
        yield


@patch('dal.tenant_dal.find_by_tenant_name')
@patch('service.catalog_service.create')
def test_create_catalog(mock_create, mock_find_tenant, client, request_headers, tenant_name, setup_request_context):
    mock_find_tenant.return_value = True
    mock_create.return_value = MockCatalogMetaData(
        name="catalog1",
        description="Description of catalog1",
        tenant_name=tenant_name,
    )

    before_request()

    response = client.post('/api/v1/catalogs', json={
        "name": "catalog1",
        "description": "Description of catalog1",
        "tenant_name": tenant_name,
    }, headers=request_headers)

    assert response.status_code == 201, f"Unexpected status code: {response.status_code}"
    assert response.json.get('name') == "catalog1", f"Unexpected catalog name: {response.json}"
    assert response.json.get('tenant_name') == tenant_name, f"Unexpected tenant name: {response.json}"
    mock_create.assert_called_once()
    mock_find_tenant.assert_called_once_with(tenant_name)


@patch('dal.tenant_dal.find_by_tenant_name')
@patch('service.catalog_service.find_by_catalog_name')
def test_get_catalog(mock_find_catalog, mock_find_tenant, client, request_headers, tenant_name, setup_request_context):
    mock_find_tenant.return_value = True
    mock_find_catalog.return_value = MockCatalogMetaData(
        name="catalog1",
        description="Description of catalog1",
        tenant_name=tenant_name,
    )

    before_request()

    response = client.get('/api/v1/catalogs/catalog1', headers=request_headers)

    assert response.status_code == 200, f"Unexpected status code: {response.status_code}"
    assert response.json.get('name') == "catalog1", f"Unexpected catalog name: {response.json}"
    assert response.json.get('tenant_name') == tenant_name, f"Unexpected tenant name: {response.json}"
    mock_find_catalog.assert_called_once_with("catalog1", tenant_name)
    mock_find_tenant.assert_called_once_with(tenant_name)
