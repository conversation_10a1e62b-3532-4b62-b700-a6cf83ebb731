from unittest.mock import patch
import pytest


class MockColumnMetaData:
    def __init__(self, id, name, description, trino_table_name, type):
        self.id = id
        self.name = name
        self.description = description
        self.trino_table_name = trino_table_name
        self.type = type

    def as_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "trino_table_name": self.trino_table_name,
            "type": self.type
        }


@pytest.fixture
def mock_columns():
    return [
        MockColumnMetaData(id=1, name="column1", description="Column 1", trino_table_name="table1", type="varchar"),
        MockColumnMetaData(id=2, name="column2", description="Column 2", trino_table_name="table1", type="int"),
    ]


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "test_tenant"))
@patch('service.column_service.find_by_table_name')
def test_get_columns_by_table(mock_find, mock_get_user_and_tenant, client, mock_columns):
    mock_find.return_value = [col.as_dict() for col in mock_columns]

    response = client.get('/api/v1/tables/test_table/columns', headers={"Tenant-Name": "test_tenant"})

    assert response.status_code == 200
    assert len(response.json) == 2
    assert response.json[0]['name'] == "column1"
    mock_find.assert_called_once_with("test_table", "test_tenant")
    mock_get_user_and_tenant.assert_called_once()


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "test_tenant"))
@patch('service.column_service.find_by_id')
def test_get_column(mock_find, mock_get_user_and_tenant, client):
    mock_column = MockColumnMetaData(
        id=1, name="column1", description="Column 1", trino_table_name="table1", type="varchar"
    )
    mock_find.return_value = mock_column.as_dict()

    response = client.get('/api/v1/columns/1', headers={"Tenant-Name": "test_tenant"})

    assert response.status_code == 200
    assert response.json['name'] == "column1"
    mock_find.assert_called_once_with("1", "test_tenant")
    mock_get_user_and_tenant.assert_called_once()


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "test_tenant"))
@patch('service.column_service.find_all')
def test_get_columns(mock_find_all, mock_get_user_and_tenant, client, mock_columns):
    mock_find_all.return_value = [col.as_dict() for col in mock_columns]

    response = client.get('/api/v1/columns', headers={"Tenant-Name": "test_tenant"})

    assert response.status_code == 200
    assert len(response.json) == 2
    assert response.json[0]['name'] == "column1"
    mock_find_all.assert_called_once_with(tenant_name="test_tenant")
    mock_get_user_and_tenant.assert_called_once()


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "test_tenant"))
@patch('service.column_service.find_by_id')
@patch('service.column_service.update')
def test_update_column(mock_update, mock_find, mock_get_user_and_tenant, client):
    existing_column = MockColumnMetaData(
        id=1, name="column1", description="Column 1", trino_table_name="table1", type="varchar"
    )
    updated_column = MockColumnMetaData(
        id=1, name="column1", description="Updated Column", trino_table_name="table1", type="varchar"
    )

    mock_find.return_value = existing_column
    mock_update.return_value = updated_column

    response = client.put('/api/v1/columns/1', json={"description": "Updated Column"},
                          headers={"Tenant-Name": "test_tenant"})

    assert response.status_code == 200
    assert response.json['description'] == "Updated Column"
    mock_find.assert_called_once_with("1", "test_tenant")
    mock_update.assert_called_once_with(existing_column)
    mock_get_user_and_tenant.assert_called_once()


@patch('interceptor.thread_context.ThreadLocalContext.get_user_and_tenant', return_value=("user1", "test_tenant"))
@patch('service.column_service.find_by_id')
@patch('service.column_service.delete')
def test_delete_column(mock_delete, mock_find, mock_get_user_and_tenant, client):
    existing_column = MockColumnMetaData(
        id=1, name="column1", description="Column 1", trino_table_name="table1", type="varchar"
    )
    mock_find.return_value = existing_column.as_dict()

    response = client.delete('/api/v1/columns/1', headers={"Tenant-Name": "test_tenant"})

    assert response.status_code == 200
    assert response.json['message'] == "ColumnMetaData deleted successfully"
    mock_find.assert_called_once_with("1", "test_tenant")
    mock_delete.assert_called_once_with(existing_column.as_dict())
    mock_get_user_and_tenant.assert_called_once()
