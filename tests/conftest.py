import pytest
from app_factory import create_app
from models.meta_data import db


@pytest.fixture(scope='module')
def app():
    app = create_app()
    app.config['TESTING'] = True

    with app.app_context():
        db.create_all()

    yield app

    with app.app_context():
        db.drop_all()


@pytest.fixture(scope='module')
def client(app):
    return app.test_client()


@pytest.fixture(scope='module')
def runner(app):
    return app.test_cli_runner()
