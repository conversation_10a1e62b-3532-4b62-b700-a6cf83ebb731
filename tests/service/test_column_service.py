import pytest
from unittest.mock import patch
import uuid
from service.column_service import create, find_all, find_by_id, update, delete, find_by_table_name
from models.meta_data import TrinoColumnMetaData


@pytest.fixture
def mock_col_meta():
    return TrinoColumnMetaData(
        id=uuid.uuid4(),
        name="column1",
        type="string",
        trino_table_name="table1",
        tenant_name="tenant1"
    )

@patch('dal.column_dal.create')
@patch('utils.LogUtils.LOG.info')
def test_create_column(mock_log_info, mock_create, mock_col_meta):
    mock_create.return_value = mock_col_meta
    result = create(mock_col_meta)

    mock_create.assert_called_once_with(mock_col_meta)
    mock_log_info.assert_called_once_with(f"ColumnMetaData created successfully - {mock_col_meta}")
    assert result == mock_col_meta

@patch('dal.column_dal.find_all')
def test_find_all_columns(mock_find_all, mock_col_meta):
    mock_find_all.return_value = [mock_col_meta]
    result = find_all(tenant_name="tenant1")

    mock_find_all.assert_called_once_with("tenant1")
    assert result == [mock_col_meta]

@patch('dal.column_dal.find_by_id')
def test_find_by_id(mock_find_by_id, mock_col_meta):
    mock_find_by_id.return_value = mock_col_meta
    result = find_by_id(id=mock_col_meta.id, tenant_name="tenant1")

    mock_find_by_id.assert_called_once_with(mock_col_meta.id, "tenant1")
    assert result == mock_col_meta

@patch('dal.column_dal.update')
@patch('utils.LogUtils.LOG.info')
def test_update_column(mock_log_info, mock_update, mock_col_meta):
    mock_update.return_value = mock_col_meta
    result = update(mock_col_meta)

    mock_update.assert_called_once_with(mock_col_meta)
    mock_log_info.assert_called_once_with(f"ColumnMetaData updated successfully - {mock_col_meta}")
    assert result == mock_col_meta

@patch('dal.column_dal.delete')
@patch('utils.LogUtils.LOG.info')
def test_delete_column(mock_log_info, mock_delete, mock_col_meta):
    delete(mock_col_meta)

    mock_delete.assert_called_once_with(mock_col_meta)
    mock_log_info.assert_called_once_with(f"ColumnMetaData deleted successfully - {mock_col_meta}")

@patch('dal.column_dal.find_by_table_name')
def test_find_by_table_name(mock_find_by_table_name, mock_col_meta):
    mock_find_by_table_name.return_value = [mock_col_meta]
    result = find_by_table_name(trino_table_name="table1", tenant_name="tenant1")

    mock_find_by_table_name.assert_called_once_with(trino_table_name="table1", tenant_name="tenant1")
    assert result == [mock_col_meta]