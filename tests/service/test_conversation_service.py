import uuid
from unittest.mock import patch, MagicMock
import pytest
from models.conversation_meta_data import Conversation
from service.conversation_service import (
    create_new_conversation,
    get_messages,
    clear_conversation,
    delete_conversation,
    get_recent_conversation, find_by_user_and_tenant
)


@patch('service.conversation_service.conversation_dal.create')
@patch('service.conversation_service.ai_svc.create_thread')
def test_create_new_conversation(mock_create_thread, mock_create):
    mock_thread = MagicMock()
    mock_thread.id = "thread1"
    mock_create_thread.return_value = mock_thread

    mock_uuid = "123e4567-e89b-12d3-a456-************"
    with patch('uuid.uuid4', return_value=uuid.UUID(mock_uuid)):
        result = create_new_conversation(
            user_id="user1",
            tenant_name="tenant1",
            module_identifier="module1",
            entity_type="type1",
            entity_id="entity1",
            entity_name="EntityName1",
        )

    assert isinstance(result, Conversation)
    assert result.id == mock_uuid
    assert result.user_id == "user1"
    assert result.openai_thread_id == "thread1"
    assert result.tenant_name == "tenant1"
    assert result.module_identifier == "module1"
    assert result.entity_type == "type1"
    assert result.entity_id == "entity1"
    assert result.entity_name == "EntityName1"

    mock_create_thread.assert_called_once()
    mock_create.assert_called_once_with(result)

def test_get_messages_unauthorized():
    mock_conversation = MagicMock()
    mock_conversation.user_id = "different_user"

    with patch('service.conversation_service.conversation_dal.find_by_id', return_value=mock_conversation):
        with pytest.raises(ValueError, match="Unauthorized"):
            get_messages(user_id="user1", tenant_name="tenant1", conversation_id="1", pageable=MagicMock())

@patch('service.conversation_service.ai_svc.delete_threads')
@patch('service.conversation_service.conversation_dal.get_thread_ids')
@patch('service.conversation_service.conversation_dal.delete_conversations_not_in_excluded_ids')
def test_clear_conversation(mock_delete_conversations, mock_get_thread_ids, mock_delete_threads):
    mock_get_thread_ids.return_value = ["thread1", "thread2"]
    mock_delete_conversations.return_value = None

    clear_conversation(user_id="user1", tenant_name="tenant1", excluded_ids=["3", "4"])

    mock_get_thread_ids.assert_called_once_with("user1", "tenant1", ["3", "4"])
    mock_delete_conversations.assert_called_once_with("user1", "tenant1", ["3", "4"])
    mock_delete_threads.assert_called_once_with(["thread1", "thread2"])

@patch('service.conversation_service.ai_svc.delete_threads')
@patch('service.conversation_service.conversation_dal.delete')
@patch('service.conversation_service.conversation_dal.find_by_id')
def test_delete_conversation(mock_find_by_id, mock_delete, mock_delete_threads):
    mock_conversation = MagicMock()
    mock_conversation.user_id = "user1"
    mock_conversation.tenant_name = "tenant1"
    mock_conversation.openai_thread_id = "thread1"

    mock_find_by_id.return_value = mock_conversation

    delete_conversation(user_id="user1", tenant_name="tenant1", conversation_id="1")

    mock_find_by_id.assert_called_once_with("1")
    mock_delete_threads.assert_called_once_with(["thread1"])
    mock_delete.assert_called_once_with(mock_conversation)

@patch('service.conversation_service.conversation_dal.find_by_user_and_tenant')
def test_get_recent_conversation(mock_find_by_user_and_tenant):
    mock_conversation = MagicMock()
    mock_find_by_user_and_tenant.return_value = ([mock_conversation], 1)

    result = get_recent_conversation(user_id="user1", tenant_name="tenant1")

    assert result == mock_conversation
    mock_find_by_user_and_tenant.assert_called_once()

    mock_find_by_user_and_tenant.return_value = ([], 0)
    result = get_recent_conversation(user_id="user1", tenant_name="tenant1")
    assert result is None

@patch('service.conversation_service.conversation_dal.find_by_user_and_tenant')
def test_find_by_user_and_tenant_no_conversations(mock_find_by_user_and_tenant):
    mock_find_by_user_and_tenant.return_value = ([], 0)

    result = find_by_user_and_tenant(user_id="user1", tenant_name="tenant1", pageable=MagicMock())
    assert result == ([], 0)

@patch('service.conversation_service.conversation_dal.get_thread_ids')
@patch('service.conversation_service.conversation_dal.delete_conversations_not_in_excluded_ids')
@patch('service.conversation_service.ai_svc.delete_threads')
def test_clear_conversation_no_excluded_ids(mock_delete_threads, mock_delete_conversations, mock_get_thread_ids):
    mock_get_thread_ids.return_value = ["thread1", "thread2"]
    mock_delete_conversations.return_value = None

    clear_conversation(user_id="user1", tenant_name="tenant1", excluded_ids=[])

    mock_get_thread_ids.assert_called_once_with("user1", "tenant1", [])
    mock_delete_conversations.assert_called_once_with("user1", "tenant1", [])
    mock_delete_threads.assert_called_once_with(["thread1", "thread2"])

@patch('service.conversation_service.conversation_dal.find_by_id')
def test_delete_conversation_not_found(mock_find_by_id):
    mock_find_by_id.return_value = None

    with pytest.raises(ValueError, match="Conversation ID not found"):
        delete_conversation(user_id="user1", tenant_name="tenant1", conversation_id="1")

@patch('service.conversation_service.conversation_dal.find_by_user_and_tenant')
def test_get_recent_conversation_not_found(mock_find_by_user_and_tenant):
    mock_find_by_user_and_tenant.return_value = ([], 0)

    result = get_recent_conversation(user_id="user1", tenant_name="tenant1")
    assert result is None
    mock_find_by_user_and_tenant.assert_called_once()