from unittest.mock import patch
import uuid
import pytest
from models.conversation_meta_data import UserQuestion
from service.user_question_service import save_user_question


@patch('service.user_question_service.user_question_dal.create')
@patch('service.user_question_service.LOG.info')
def test_save_user_question(mock_log_info, mock_create):
    mock_uuid = "123e4567-e89b-12d3-a456-************"
    with patch('uuid.uuid4', return_value=uuid.UUID(mock_uuid)):
        result = save_user_question(
            user_id="user1",
            question="What is the capital of France?",
            conversation_id="conversation1",
            module_identifier="module1"
        )

    assert isinstance(result, UserQuestion)
    assert result.id == mock_uuid
    assert result.user_id == "user1"
    assert result.question == "What is the capital of France?"
    assert result.conversation_id == "conversation1"
    assert result.module_identifier == "module1"

    mock_create.assert_called_once_with(result)
    mock_log_info.assert_called_once_with(f"User Question saved successfully - {result}")

@patch('service.user_question_service.user_question_dal.create', side_effect=Exception("Database Error"))
@patch('service.user_question_service.LOG.info')
def test_save_user_question_exception(mock_log_info, mock_create):
    with pytest.raises(Exception, match="Database Error"):
        save_user_question(
            user_id="user1",
            question="What is the capital of France?",
            conversation_id="conversation1",
            module_identifier="module1"
        )

    mock_create.assert_called_once()
    mock_log_info.assert_not_called()

@patch('service.user_question_service.user_question_dal.create')
@patch('service.user_question_service.LOG.info')
def test_save_user_question_special_characters(mock_log_info, mock_create):
    mock_uuid = "123e4567-e89b-12d3-a456-************"
    special_question = "What does 2+2 equal? 🤔"

    with patch('uuid.uuid4', return_value=uuid.UUID(mock_uuid)):
        result = save_user_question(
            user_id="user2",
            question=special_question,
            conversation_id="conversation2",
            module_identifier="module2"
        )

    assert isinstance(result, UserQuestion)
    assert result.id == mock_uuid
    assert result.question == special_question

    mock_create.assert_called_once_with(result)
    mock_log_info.assert_called_once_with(f"User Question saved successfully - {result}")

@patch('service.user_question_service.user_question_dal.create')
@patch('service.user_question_service.LOG.info')
def test_save_user_question_missing_parameters(mock_log_info, mock_create):
    with pytest.raises(TypeError, match="missing 1 required positional argument: 'module_identifier'"):
        save_user_question(
            user_id="user3",
            question="This is an incomplete test",
            conversation_id="conversation3"
        )

    mock_create.assert_not_called()
    mock_log_info.assert_not_called()