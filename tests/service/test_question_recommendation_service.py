import pytest
from unittest.mock import patch, Mock
from service.question_recommendation_service import (
    get_recommended_questions_for_chat,
    get_recommended_questions_for_user_and_conversation,
    get_recommended_questions_for_user
)
from datetime import datetime


@pytest.fixture
def mock_questions():
    return [
        Mock(
            id="1",
            user_id="user-001",
            conversation_id="conv-001",
            question="What is AI?",
            module_identifier="module-001",
            created_on=datetime(2023, 1, 1)
        ),
        Mock(
            id="2",
            user_id="user-001",
            conversation_id="conv-001",
            question="How does machine learning work?",
            module_identifier="module-001",
            created_on=datetime(2023, 1, 2)
        ),
        Mock(
            id="3",
            user_id="user-001",
            conversation_id="conv-001",
            question="What are neural networks?",
            module_identifier="module-001",
            created_on=datetime(2023, 1, 3)
        ),
    ]


@pytest.fixture
def mock_recommendations():
    return [
        "What is AI?",
        "How does machine learning work?",
        "What are neural networks?"
    ]

@patch('dal.question_recommendation_dal.find_all_recent_questions')
def test_get_recommended_questions_for_user_and_conversation_insufficient_questions(mock_find_questions):
    mock_find_questions.return_value = []

    user_id = "user-001"
    conversation_id = "conv-001"
    module_identifier = "module-001"
    entity_type = "entity-001"

    recommendations = get_recommended_questions_for_user_and_conversation(
        user_id, conversation_id, module_identifier, entity_type
    )

    assert recommendations == []
    mock_find_questions.assert_called_once()

@patch('dal.question_recommendation_dal.find_all_recent_questions')
def test_get_recommended_questions_for_user_insufficient_questions(mock_find_questions):
    mock_find_questions.return_value = []

    user_id = "user-001"
    module_identifier = "module-001"
    entity_type = "entity-001"

    recommendations = get_recommended_questions_for_user(
        user_id, module_identifier, entity_type
    )

    assert recommendations == []
    mock_find_questions.assert_called_once()

@patch('service.question_recommendation_service.get_recommended_questions_for_user_and_conversation')
@patch('service.question_recommendation_service.get_recommended_questions_for_user')
def test_get_recommended_questions_for_chat(mock_get_for_user, mock_get_for_conversation, mock_recommendations):
    user_id = "user-001"
    module_identifier = "module-001"
    conversation_id = "conv-001"
    entity_type = "entity-001"

    mock_get_for_conversation.return_value = mock_recommendations
    recommendations = get_recommended_questions_for_chat(
        user_id, module_identifier, conversation_id, entity_type
    )
    assert len(recommendations) == 3
    mock_get_for_conversation.assert_called_once_with(
        user_id, conversation_id, module_identifier, entity_type
    )

    mock_get_for_user.return_value = mock_recommendations
    recommendations = get_recommended_questions_for_chat(
        user_id, module_identifier, None, entity_type
    )
    assert len(recommendations) == 3
    mock_get_for_user.assert_called_once_with(
        user_id, module_identifier, entity_type
    )