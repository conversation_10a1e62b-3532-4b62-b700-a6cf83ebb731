import pytest
from unittest.mock import patch
from service.db_service import create, find_all, find_by_db_name, update, delete, find_by_catalog_name
from models.meta_data import TrinoDBMetaData


@pytest.fixture
def mock_db_meta():
    return TrinoDBMetaData(
        name="db1",
        description="Description of db1",
        trino_catalog_name="catalog1",
        tenant_name="tenant1"
    )

@patch('dal.db_dal.create')
def test_create_db(mock_create, mock_db_meta):
    mock_create.return_value = mock_db_meta
    result = create(mock_db_meta)

    mock_create.assert_called_once_with(mock_db_meta)
    assert result == mock_db_meta

@patch('dal.db_dal.find_all')
def test_find_all_dbs(mock_find_all, mock_db_meta):
    mock_find_all.return_value = [mock_db_meta]
    result = find_all(tenant_name="tenant1")

    mock_find_all.assert_called_once_with("tenant1")
    assert result == [mock_db_meta]

@patch('dal.db_dal.find_by_db_name')
def test_find_by_db_name(mock_find_by_name, mock_db_meta):
    mock_find_by_name.return_value = mock_db_meta
    result = find_by_db_name(name="db1", tenant_name="tenant1")

    mock_find_by_name.assert_called_once_with("db1", "tenant1")
    assert result == mock_db_meta

@patch('dal.db_dal.update')
def test_update_db(mock_update, mock_db_meta):
    mock_update.return_value = mock_db_meta
    result = update(mock_db_meta)

    mock_update.assert_called_once_with(mock_db_meta)
    assert result == mock_db_meta

@patch('dal.db_dal.delete')
def test_delete_db(mock_delete, mock_db_meta):
    delete(mock_db_meta)

    mock_delete.assert_called_once_with(mock_db_meta)

@patch('dal.db_dal.find_by_catalog_name')
def test_find_by_catalog_name(mock_find_by_catalog_name, mock_db_meta):
    mock_find_by_catalog_name.return_value = [mock_db_meta]
    result = find_by_catalog_name(trino_catalog_name="catalog1", tenant_name="tenant1")

    mock_find_by_catalog_name.assert_called_once_with(trino_catalog_name="catalog1", tenant_name="tenant1")
    assert result == [mock_db_meta]