import pytest
from unittest.mock import patch
from service.table_service import create, find_all, find_by_table_name, update, delete, find_by_db_name
from models.meta_data import TrinoTableMetaData


@pytest.fixture
def mock_table_meta():
    return TrinoTableMetaData(
        name="table1",
        description="Description of table1",
        trino_db_name="db1",
        tenant_name="tenant1"
    )

@patch('dal.table_dal.create')
@patch('utils.LogUtils.LOG.info')
def test_create_table(mock_log_info, mock_create, mock_table_meta):
    mock_create.return_value = mock_table_meta
    result = create(mock_table_meta)

    mock_create.assert_called_once_with(mock_table_meta)
    mock_log_info.assert_called_once_with(f"TableMetaData created successfully - {mock_table_meta}")
    assert result == mock_table_meta

@patch('dal.table_dal.find_all')
def test_find_all_tables(mock_find_all, mock_table_meta):
    mock_find_all.return_value = [mock_table_meta]
    result = find_all(tenant_name="tenant1")

    mock_find_all.assert_called_once_with("tenant1")
    assert result == [mock_table_meta]

@patch('dal.table_dal.find_by_table_name')
def test_find_by_table_name(mock_find_by_name, mock_table_meta):
    mock_find_by_name.return_value = mock_table_meta
    result = find_by_table_name(name="table1", tenant_name="tenant1")

    mock_find_by_name.assert_called_once_with("table1", "tenant1")
    assert result == mock_table_meta

@patch('dal.table_dal.update')
@patch('utils.LogUtils.LOG.info')
def test_update_table(mock_log_info, mock_update, mock_table_meta):
    mock_update.return_value = mock_table_meta
    result = update(mock_table_meta)

    mock_update.assert_called_once_with(mock_table_meta)
    mock_log_info.assert_called_once_with(f"TableMetaData updated successfully - {mock_table_meta}")
    assert result == mock_table_meta

@patch('dal.table_dal.delete')
@patch('utils.LogUtils.LOG.info')
def test_delete_table(mock_log_info, mock_delete, mock_table_meta):
    delete(mock_table_meta)

    mock_delete.assert_called_once_with(mock_table_meta)
    mock_log_info.assert_called_once_with(f"TableMetaData deleted successfully - {mock_table_meta}")

@patch('dal.table_dal.find_by_db_name')
def test_find_by_db_name(mock_find_by_db_name, mock_table_meta):
    mock_find_by_db_name.return_value = [mock_table_meta]
    result = find_by_db_name(trino_db_name="db1", tenant_name="tenant1")

    mock_find_by_db_name.assert_called_once_with(trino_db_name="db1", tenant_name="tenant1")
    assert result == [mock_table_meta]