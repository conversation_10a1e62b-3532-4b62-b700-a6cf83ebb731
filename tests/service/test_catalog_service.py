import pytest
from unittest.mock import patch
from service.catalog_service import create, find_all, find_by_catalog_name, update, delete
from models.meta_data import TrinoCatalogMetaData


@pytest.fixture
def mock_catalog():
    return TrinoCatalogMetaData(
        name="catalog1",
        description="Description of catalog1",
        tenant_name="tenant1"
    )

@patch('dal.catalog_dal.create')
def test_create_catalog(mock_create, mock_catalog):
    mock_create.return_value = mock_catalog
    result = create(mock_catalog)

    mock_create.assert_called_once_with(mock_catalog)
    assert result == mock_catalog

@patch('dal.catalog_dal.find_all')
def test_find_all_catalogs(mock_find_all, mock_catalog):
    mock_find_all.return_value = [mock_catalog]
    result = find_all(tenant_name="tenant1")

    mock_find_all.assert_called_once_with("tenant1")
    assert result == [mock_catalog]

@patch('dal.catalog_dal.find_by_catalog_name')
def test_find_by_catalog_name(mock_find_by_name, mock_catalog):
    mock_find_by_name.return_value = mock_catalog
    result = find_by_catalog_name(name="catalog1", tenant_name="tenant1")

    mock_find_by_name.assert_called_once_with("catalog1", "tenant1")
    assert result == mock_catalog


@patch('dal.catalog_dal.update')
def test_update_catalog(mock_update, mock_catalog):
    mock_update.return_value = mock_catalog
    result = update(mock_catalog)

    mock_update.assert_called_once_with(mock_catalog)
    assert result == mock_catalog

@patch('dal.catalog_dal.delete')
def test_delete_catalog(mock_delete, mock_catalog):
    delete(mock_catalog)

    mock_delete.assert_called_once_with(mock_catalog)