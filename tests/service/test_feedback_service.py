import uuid
import pytest
from unittest.mock import patch
from models.conversation_meta_data import User<PERSON><PERSON><PERSON><PERSON>
from service.feedback_service import (
    find_feedbacks_by_filters,
    find_feedbacks_by_user_and_thread,
    submit_feedback
)
from utils.PaginationUtils import Pageable, SortOrder


def mock_feedback():
    return UserFeedBack(
        id=str(uuid.uuid4()),
        user_id="user-001",
        tenant_name="org-001",
        conversation_id="conv-001",
        comment="Great response!"
    )

@patch('service.conversation_service.get_and_authorize_conversation')
def test_submit_feedback_unauthorized_conversation(mock_get_and_authorize_conversation):
    mock_get_and_authorize_conversation.side_effect = Exception("Unauthorized")

    with pytest.raises(Exception) as exc_info:
        submit_feedback(
            feedback=mock_feedback()
        )

    assert str(exc_info.value) == "Unauthorized"
    mock_get_and_authorize_conversation.assert_called_once_with("user-001", "org-001", "conv-001")

@patch('dal.feedback_dal.find_feedbacks_with_pagination_and_sorting')
def test_find_feedbacks_by_filters(mock_find_feedbacks_with_pagination_and_sorting):
    mock_feedbacks = [mock_feedback()]
    mock_find_feedbacks_with_pagination_and_sorting.return_value = (mock_feedbacks, 1)

    filters = {'user_id': 'user-001', 'tenant_name': 'org-001'}
    page_number = 1
    page_size = 10
    sort_by = 'createdAt'
    sort_order = 'desc'

    pageable = Pageable(
        page_number=page_number,
        page_size=page_size,
        sort_by=sort_by,
        sort_order=SortOrder[sort_order.upper()]
    )

    result_feedbacks, total = find_feedbacks_by_filters(filters, pageable)

    mock_find_feedbacks_with_pagination_and_sorting.assert_called_once_with(filters, pageable)

    assert len(result_feedbacks) == 1
    assert total == 1
    assert result_feedbacks[0].user_id == "user-001"

@patch('dal.feedback_dal.find_feedbacks_with_pagination_and_sorting')
def test_find_feedbacks_by_filters_no_results(mock_find_feedbacks_with_pagination_and_sorting):
    mock_find_feedbacks_with_pagination_and_sorting.return_value = ([], 0)

    filters = {}
    page_number = 1
    page_size = 10
    sort_by = 'createdAt'
    sort_order = 'desc'

    pageable = Pageable(
        page_number=page_number,
        page_size=page_size,
        sort_by=sort_by,
        sort_order=SortOrder[sort_order.upper()]
    )

    result_feedbacks, total = find_feedbacks_by_filters(filters, pageable)

    mock_find_feedbacks_with_pagination_and_sorting.assert_called_once_with(filters, pageable)

    assert len(result_feedbacks) == 0
    assert total == 0

@patch('dal.feedback_dal.find_feedbacks_with_pagination_and_sorting')
def test_find_feedbacks_by_filters_empty_last_page(mock_find_feedbacks_with_pagination_and_sorting):
    mock_find_feedbacks_with_pagination_and_sorting.return_value = ([], 5)

    filters = {}
    page_number = 2
    page_size = 5
    sort_by = 'createdAt'
    sort_order = 'asc'

    pageable = Pageable(
        page_number=page_number,
        page_size=page_size,
        sort_by=sort_by,
        sort_order=SortOrder[sort_order.upper()]
    )

    result_feedbacks, total = find_feedbacks_by_filters(filters, pageable)

    mock_find_feedbacks_with_pagination_and_sorting.assert_called_once_with(filters, pageable)

    assert len(result_feedbacks) == 0
    assert total == 5

@patch('dal.feedback_dal.find_feedbacks_by_user_and_thread')
def test_find_feedbacks_by_user_and_thread(mock_find_feedbacks_by_user_and_thread):
    mock_feedbacks = [mock_feedback()]
    mock_find_feedbacks_by_user_and_thread.return_value = mock_feedbacks

    result = find_feedbacks_by_user_and_thread(
        user_id="user-001",
        tenant_name="org-001",
        conversation_id="conv-001"
    )

    mock_find_feedbacks_by_user_and_thread.assert_called_once_with("user-001", "org-001", "conv-001")
    assert len(result) == 1
    assert result[0].user_id == "user-001"

@patch('dal.feedback_dal.find_feedbacks_by_user_and_thread')
def test_find_feedbacks_by_user_and_thread_no_results(mock_find_feedbacks_by_user_and_thread):
    mock_find_feedbacks_by_user_and_thread.return_value = []

    result = find_feedbacks_by_user_and_thread(
        user_id="user-001",
        tenant_name="org-001",
        conversation_id="conv-001"
    )

    mock_find_feedbacks_by_user_and_thread.assert_called_once_with("user-001", "org-001", "conv-001")
    assert result == []

@patch('dal.feedback_dal.create')
@patch('service.conversation_service.get_and_authorize_conversation')
def test_submit_feedback_success(mock_get_and_authorize_conversation, mock_create):
    mock_get_and_authorize_conversation.return_value = True  # Simulate successful authorization
    mock_create.return_value = mock_feedback()

    feedback = mock_feedback()
    result = submit_feedback(
        feedback=feedback
    )

    mock_create.assert_called_once_with(feedback)
    assert result == feedback