version: '3.8'

services:
  platform-ai-db-service:
    image: ankane/pgvector
    environment:
      POSTGRES_DB: platform_ai_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - pgvector_data:/var/lib/postgresql/data
    networks:
      - platform-ai-trail-nw

  trino:
    image: trinodb/trino:400
    container_name: platform-ai-trino-db
    ports:
      - "8080:8080"
    volumes:
       - ./trino/etc:/etc/trino
    networks:
      - platform-ai-trail-nw

  platform-ai-app:
    build: .
    image: platform-ai-app:local
    container_name: platform-ai-app
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: **********************************************************/platform_ai_service
      TRINO_HOST: trino
      TRINO_PORT: 8080
#      WERKZEUG_RUN_MAIN: true
#      WERKZEUG_SERVER_FD: 3

    depends_on:
      - platform-ai-db-service
    networks:
      - platform-ai-trail-nw


volumes:
  pgvector_data:

networks:
  platform-ai-trail-nw: