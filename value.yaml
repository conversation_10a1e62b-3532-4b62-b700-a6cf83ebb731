replicaCount: 1
image:
  repository: ""
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: ""
ingress:
  enabled: false
  host: test
service:
  type: ClusterIP
  port: 80
  targetport: 3000
container:
  port: 3000

envfrom:
  - configMapRef:
      name: platform-ai-config
  - secretRef:
      name: aurora-secret    
  - secretRef:
      name: platform-ai-secret

resources: 
  limits:
    cpu: 800m
    memory: 1024Mi
  requests:
    cpu: 300m
    memory: 200Mi

livenessProbe:
  enabled: false
  path: /health
  initialDelaySeconds: 10
  periodSeconds: 5
  timeoutSeconds: 10
  successThreshold: 1
  failureThreshold: 2

readinessProbe:
  enabled: false
  path: /health
  initialDelaySeconds: 10
  periodSeconds: 5
  timeoutSeconds: 10 
  successThreshold: 1
  failureThreshold: 2
  
nodeSelector: {}

tolerations: []

affinity: 
   enabled: false

