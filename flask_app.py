import os
import subprocess
import sys
import config

from flasgger import Swagger
from interceptor.interceptor import before_request, after_request
from migrations.migration_manager import generate_yoyo_migration, apply_yoyo_migrations
from utils.global_exception_handler import register_error_handlers

config.load_env_variables()
env = os.environ.copy()
env['PYTHONPATH'] = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

from app_factory import create_app
from constants.app_constants import APP_MODE_LANGCHAIN, APP_MODE_OPEN_AI_ASSISTANT
from swagger.swagger_generator import swagger_template
from utils.LogUtils import LOG

# from flask_cors import CORS

app = create_app()
# CORS(app)

swagger = Swagger(app, template=swagger_template)

# Register interceptors (before_request and after_request)
app.before_request(before_request)
app.after_request(after_request)

# Register error handlers
register_error_handlers(app)


def run_streamlit():
    llm_mode = os.getenv('LLM_MODE')
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        if llm_mode == APP_MODE_LANGCHAIN:
            LOG.info("Running langchain_streamlit_app in subprocess")
            subprocess.Popen([
                sys.executable,
                "-m",
                "streamlit",
                "run",
                "ui/langchain_streamlit_app.py",
            ], env=env)

            subprocess.Popen(["streamlit", "run", "ui/langchain_streamlit_app.py"])
        elif llm_mode == APP_MODE_OPEN_AI_ASSISTANT:
            LOG.info("Running openai_assistant_streamlit_app in subprocess")
            subprocess.Popen([
                sys.executable,
                "-m",
                "streamlit",
                "run",
                "ui/openai_assistant_streamlit_app.py",
                # Add any additional Streamlit arguments here
            ], env=env)


if __name__ == '__main__':
    #run_streamlit()
    # Step 1: Generate migration script
    LOG.info("Checking for schema changes and generating migrations if needed...")
    generate_yoyo_migration()

    # Step 2: Apply migrations
    LOG.info("Applying Yoyo migrations...")
    apply_yoyo_migrations()

    # Step 3: Start Flask app
    app.run(host="0.0.0.0", debug=False, port=3001)
