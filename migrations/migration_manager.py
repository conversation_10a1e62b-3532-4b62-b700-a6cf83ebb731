import os
import sys
from datetime import datetime
from yoyo import read_migrations, get_backend
from sqlalchemy import create_engine, MetaData, Column, Table
from alembic.migration import MigrationContext
from alembic.autogenerate import compare_metadata
from models.meta_data import db
from utils.LogUtils import LOG
from config.app_config import Config

MIGRATIONS_FOLDER = os.path.dirname(os.path.abspath(__file__))

# Referring yoyo-migrations for DB schema migrations - https://pypi.org/project/yoyo-migrations/


def get_database_url():
    """Fetch the DATABASE_URL from environment variables."""
    db_url = Config.SQLALCHEMY_DATABASE_URI
    if not db_url:
        LOG.error("DATABASE_URL is not set. Aborting migration.")
        sys.exit(1)
    return db_url


def generate_yoyo_migration():
    """Generates a Yoyo migration script if schema changes are detected."""
    db_url = get_database_url()
    engine = create_engine(db_url)
    conn = engine.connect()
    meta = MetaData()
    meta.reflect(bind=engine)  # Get current DB schema

    migration_ctx = MigrationContext.configure(conn)
    excluded_tables = {"_yoyo_migration", "_yoyo_log", "_yoyo_version", "yoyo_lock"}

    diff = [
        change for change in compare_metadata(migration_ctx, db.metadata)
        if not (change[0] == "remove_table" and change[1].name in excluded_tables)
    ]

    if not diff:
        LOG.info("No schema changes detected.")
        return
    else:
        LOG.info(f"Schema changes detected: {diff}")

    migration_script = []

    for diff_entry in diff:
        if isinstance(diff_entry, list):  # Fix for extra list wrapping issue
            diff_entry = diff_entry[0]
        action = diff_entry[0]  # Always the action type
        obj_name = diff_entry[1] if len(diff_entry) > 1 else None  # Ensure index safety
        details = diff_entry[2:] if len(diff_entry) > 2 else []  # Avoid out-of-range errors

        obj_name = str(obj_name) if obj_name is not None else None

        if action == "add_table" and obj_name:
            columns = ", ".join(
                f"{col.name} {col.type}" for col in db.metadata.tables[obj_name].columns
            )
            LOG.info(f"Generating migration: CREATE TABLE {obj_name} ({columns});")
            migration_script.append(f"CREATE TABLE {obj_name} ({columns});")

        if action == "remove_table" and obj_name:
            LOG.info(f"Generating migration: DROP TABLE {obj_name};")
            migration_script.append(f"DROP TABLE {obj_name};")


        elif action == "add_column":
            if len(details) >= 2:
                table_name, column_obj = details[:2]  # Extract table name and column object
                if isinstance(column_obj, Column):
                    column_name = column_obj.name  # Get column name
                    not_null = "NOT NULL" if not column_obj.nullable else ""
                    LOG.info(f"Generating migration: ALTER TABLE {table_name} ADD COLUMN IF NOT EXISTS {column_name} {column_obj.type} {not_null};")
                    migration_script.append(
                        f"ALTER TABLE {table_name} ADD COLUMN IF NOT EXISTS {column_name} {column_obj.type} {not_null};")
                else:
                    LOG.warning(f"Skipping add_column due to unexpected column type: {column_obj}")
            else:
                LOG.warning(f"Skipping add_column due to missing details: {details}")

        elif action == "remove_column":
            if len(details) >= 2:
                table_name, column_obj = details[:2]
                if isinstance(column_obj, Column):
                    column_name = column_obj.name
                    LOG.info(f"Generating migration: ALTER TABLE {table_name} DROP COLUMN IF EXISTS {column_name};")
                    migration_script.append(f"ALTER TABLE {table_name} DROP COLUMN IF EXISTS {column_name};")
                else:
                    LOG.warning(f"Skipping remove_column due to unexpected column type: {column_obj}")
            else:
                LOG.warning(f"Skipping remove_column due to missing details: {details}")


        elif action == "modify_nullable":
            if len(details) < 5:
                LOG.warning(f"Skipping modify_nullable due to missing details: {details}")
                continue  # Prevents incomplete processing
            table_name, column_name, column_info, old_nullable, new_nullable = details[:5]

            if old_nullable is False and new_nullable is True:
                LOG.info(f"Generating migration: ALTER TABLE {table_name} ALTER COLUMN {column_name} DROP NOT NULL;")
                migration_script.append(f"ALTER TABLE {table_name} ALTER COLUMN {column_name} DROP NOT NULL;")

            elif old_nullable is True and new_nullable is False:
                LOG.info(f"Generating migration: ALTER TABLE {table_name} ALTER COLUMN {column_name} SET NOT NULL;")
                migration_script.append(f"ALTER TABLE {table_name} ALTER COLUMN {column_name} SET NOT NULL;")
            else:
                LOG.warning(f"Skipping modify_nullable - No actual change detected: {diff_entry}")

        else:
            LOG.warning(f"Skipping unsupported schema change: {action} - {obj_name}")

    if migration_script:
        migration_filename = f"{MIGRATIONS_FOLDER}/{datetime.now().strftime('%d%m%Y_%H%M%S')}_auto_migration.sql"
        with open(migration_filename, "w") as f:
            f.write("\n".join(migration_script) + "\n")
        LOG.info(f"New migration generated: {migration_filename}")
    else:
        LOG.info("No migration needed.")


def apply_yoyo_migrations():
    backend = get_backend(Config.SQLALCHEMY_DATABASE_URI)
    migrations = read_migrations(MIGRATIONS_FOLDER)

    applied_migrations = set(backend.get_applied_migration_hashes())  # Get already applied migrations
    to_apply = migrations.filter(lambda m: m.hash not in applied_migrations)  # Correct filtering of new migrations

    LOG.info(f"Migrations to apply: {to_apply}")
    if to_apply:
        backend.apply_migrations(to_apply)
        LOG.info("Successfully applied Yoyo migrations.")
    else:
        LOG.info("No new migrations to apply.")