FROM python:3.9-slim as build-stage

WORKDIR /app

COPY requirements.txt requirements.txt

RUN apt-get update && \
    apt-get install -y libpq-dev && \
    pip3 install --no-cache-dir -r requirements.txt

COPY . .

FROM sonarsource/sonar-scanner-cli:4.6 AS sonarqube_scan
ARG SONAR_LOGIN
ARG SONAR_URL
ARG PROJECT_NAME
ARG VERSION
WORKDIR /app
COPY . .
COPY --from=build-stage /app /app/


RUN sonar-scanner \
    -Dsonar.host.url="$SONAR_URL" \
    -Dsonar.login="$SONAR_LOGIN" \
    -Dsonar.projectKey="$PROJECT_NAME" \
    -Dsonar.sources="."

