#!/usr/bin/env python3
"""
Test script to verify API integration and question_query_history table population
"""

import sys
import os
import requests
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from config.app_config import Config
from models.meta_data import db
from dal.question_query_history_dal import get_all_question_query_pairs

def create_test_app():
    """Create a test Flask app with database configuration"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def test_conversation_creation():
    """Test creating a new conversation"""
    print("🧪 Testing conversation creation...")
    
    url = "http://localhost:3001/api/v1/conversations"
    headers = {
        "User-Id": "test-user-123",
        "Tenant-Name": "stage",
        "Content-Type": "application/json"
    }
    params = {
        "module_identifier": "event-timeline",
        "entity_type": "vessel",
        "entity_id": "IMO123456"
    }
    
    try:
        response = requests.post(url, headers=headers, params=params)
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Response: {response.text}")
        
        if response.status_code == 201:
            conversation_data = response.json()
            conversation_id = conversation_data.get('id')
            print(f"✅ Conversation created successfully! ID: {conversation_id}")
            return conversation_id
        else:
            print(f"❌ Failed to create conversation: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error creating conversation: {e}")
        return None

def test_question_api(conversation_id):
    """Test sending a question to the conversation API"""
    print(f"\n🧪 Testing question API with conversation ID: {conversation_id}")
    
    url = f"http://localhost:3001/api/v1/conversations/{conversation_id}/messages"
    headers = {
        "User-Id": "test-user-123",
        "Tenant-Name": "stage",
        "Content-Type": "application/json"
    }
    data = {
        "question": "How many vessels are there in this account?"
    }
    
    try:
        print("📤 Sending question...")
        response = requests.post(url, headers=headers, json=data)
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Question API call successful!")
            return True
        else:
            print(f"❌ Question API call failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling question API: {e}")
        return False

def check_question_query_history():
    """Check if entries were added to question_query_history table"""
    print("\n📊 Checking question_query_history table...")
    
    app = create_test_app()
    with app.app_context():
        try:
            entries = get_all_question_query_pairs(limit=10)
            print(f"📈 Total entries in question_query_history: {len(entries)}")
            
            # Show recent entries
            for i, entry in enumerate(entries[:5]):
                print(f"  {i+1}. Question: {entry.question_text}")
                print(f"     Queries: {len(entry.queries)} queries")
                print(f"     Created: {entry.created_at}")
                if entry.queries:
                    print(f"     Sample Query: {entry.queries[0][:100]}...")
                print()
                
            return len(entries) > 0
            
        except Exception as e:
            print(f"❌ Error checking question_query_history: {e}")
            return False

def test_direct_insertion():
    """Test direct insertion into question_query_history table"""
    print("\n🧪 Testing direct insertion...")
    
    from dal.question_query_history_dal import create_question_query_entry
    
    app = create_test_app()
    with app.app_context():
        try:
            test_question = "Test API integration question"
            test_queries = [
                "SELECT COUNT(*) FROM test_table",
                "SELECT * FROM test_table LIMIT 10"
            ]
            
            create_question_query_entry(test_question, test_queries)
            print("✅ Direct insertion successful!")
            return True
            
        except Exception as e:
            print(f"❌ Direct insertion failed: {e}")
            return False

def main():
    """Main test function"""
    print("🚀 Starting API integration tests...")
    print("=" * 60)
    
    # Test 1: Check if Flask app is running
    try:
        response = requests.get("http://localhost:3001/api/v1/catalogs", 
                              headers={"User-Id": "test", "Tenant-Name": "stage"})
        if response.status_code == 200:
            print("✅ Flask app is running on port 3001")
        else:
            print("❌ Flask app not responding correctly")
            return
    except Exception as e:
        print(f"❌ Flask app not accessible: {e}")
        print("Please make sure the Flask app is running on port 3001")
        return
    
    # Test 2: Direct insertion test
    test_direct_insertion()
    
    # Test 3: Check initial state
    print("\n📊 Initial state of question_query_history table:")
    initial_count = 0
    app = create_test_app()
    with app.app_context():
        try:
            entries = get_all_question_query_pairs(limit=1)
            initial_count = len(get_all_question_query_pairs(limit=1000))
            print(f"Initial entries: {initial_count}")
        except Exception as e:
            print(f"Error checking initial state: {e}")
    
    # Test 4: Create conversation
    conversation_id = test_conversation_creation()
    if not conversation_id:
        print("❌ Cannot proceed without conversation ID")
        return
    
    # Test 5: Send question
    question_success = test_question_api(conversation_id)
    
    # Test 6: Check if question_query_history was updated
    time.sleep(2)  # Wait a bit for async processing
    final_success = check_question_query_history()
    
    # Test 7: Check if new entries were added
    with app.app_context():
        try:
            final_count = len(get_all_question_query_pairs(limit=1000))
            new_entries = final_count - initial_count
            print(f"\n📈 Summary:")
            print(f"   Initial entries: {initial_count}")
            print(f"   Final entries: {final_count}")
            print(f"   New entries added: {new_entries}")
            
            if new_entries > 0:
                print("✅ New entries were added to question_query_history!")
            else:
                print("⚠️ No new entries were added to question_query_history")
                
        except Exception as e:
            print(f"Error in final count: {e}")
    
    print("\n🎯 Test Results:")
    print(f"   ✅ Direct insertion: {'✅' if test_direct_insertion else '❌'}")
    print(f"   ✅ API call: {'✅' if question_success else '❌'}")
    print(f"   ✅ Database check: {'✅' if final_success else '❌'}")
    
    print("\n✅ Integration tests completed!")

if __name__ == "__main__":
    main()
