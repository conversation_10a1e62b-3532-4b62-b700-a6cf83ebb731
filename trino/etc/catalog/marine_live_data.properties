connector.name=mongodb

# If you are running app and db locally
#
# connection-url = mongodb://localhost:27017

# If you are running via port forward and app locally
# sshpass -p '7awRqgcnWwKBBzZg' ssh -L localhost:27017:************:27017 hull-integration@************
#
# connection-url=****************************************************

# If you are running app via docker and connecting to remote DB via tunnel
# sshpass -p '7awRqgcnWwKBBzZg' ssh -L localhost:27017:************:27017 hull-integration@************
#
# connection-url=mongodb://questmarine:<EMAIL>:27017
# This setting may not work on some linux distros

# If you are running on EC2
# Then we dont need ssh tunnel
#
# ****************************************************************************************************************************


mongodb.connection-url=mongodb://questmarine:<EMAIL>:27017/admin?replicaSet=qmrs0i5
mongodb.case-insensitive-name-matching=true
