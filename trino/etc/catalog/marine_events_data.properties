connector.name=postgresql

# If you are running app and db locally
# connection-url = ********************************************

# If you are running via port forward and app locally
# sshpass -p '7awRqgcnWwKBBzZg' ssh -L localhost:5435:hull-nonprod-aurora-pg.c3jfjjzaly5m.eu-west-2.rds.amazonaws.com:5432 hull-integration@************
# connection-url=********************************************

# If you are running app via docker and connecting to remote DB via tunnel
# sshpass -p '7awRqgcnWwKBBzZg' ssh -L localhost:5435:hull-nonprod-aurora-pg.c3jfjjzaly5m.eu-west-2.rds.amazonaws.com:5432 hull-integration@************
# connection-url=*******************************************************
# This setting may not work on some linux distros

# If you are running on EC2
# Then we dont need ssh tunnel
# connection-url=**************************************************************************************************

connection-url=*******************************************************
connection-user=hull_np_read_user
connection-password=1AQ6^oo3u8Ow
postgresql.array-mapping=AS_ARRAY
