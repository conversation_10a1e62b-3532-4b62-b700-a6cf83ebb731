import uuid

from models.meta_data import db, TrinoColumnMetaData


def create(col_meta_data: TrinoColumnMetaData):
    db.session.add(col_meta_data)
    db.session.commit()


def find_all(tenant_name: str):
    return TrinoColumnMetaData.query.filter(TrinoColumnMetaData.tenant_name.in_(['*', tenant_name])).all()


def find_by_id(id: uuid, tenant_name: str):
    return TrinoColumnMetaData.query.filter(
        TrinoColumnMetaData.id == id,
        TrinoColumnMetaData.tenant_name.in_(['*', tenant_name])
    ).first()


def update(col_meta_data: TrinoColumnMetaData):
    db.session.add(col_meta_data)
    db.session.commit()


def delete(col_meta_data: TrinoColumnMetaData):
    db.session.delete(col_meta_data)
    db.session.commit()


def find_by_table_name(trino_table_name: str, tenant_name: str):
    return TrinoColumnMetaData.query.filter(
        TrinoColumnMetaData.trino_table_name == trino_table_name,
        TrinoColumnMetaData.tenant_name.in_(['*', tenant_name])
    ).all()
