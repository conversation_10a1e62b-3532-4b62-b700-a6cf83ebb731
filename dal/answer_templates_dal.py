from functools import lru_cache
from langchain_openai import OpenAIEmbeddings
from config.app_config import Config
from models.conversation_meta_data import db, AnswerTemplate

embedding_model = OpenAIEmbeddings(model=Config.OPEN_AI_EMBEDDINGS,max_retries=1,timeout=10)
#embedding_model = GoogleGenerativeAIEmbeddings(model="models/gemini-embedding-exp-03-07")


def create(example: AnswerTemplate):
    db.session.add(example)
    db.session.commit()


def get_similar_answer_templates(question_template: str, top_n) -> [AnswerTemplate]:
    question_template_embedding = embedding_model.embed_query(question_template)
    result = (db.session.query(AnswerTemplate)
              .order_by(AnswerTemplate.question_template_embedding.cosine_distance(question_template_embedding))
              .limit(top_n).all())
    return result
