from models.meta_data import db, TrinoCatalogMetaData


def create(catalog: TrinoCatalogMetaData):
    db.session.add(catalog)
    db.session.commit()


def find_all(tenant_name: str):
    return TrinoCatalogMetaData.query.filter(TrinoCatalogMetaData.tenant_name.in_(['*', tenant_name])).all()


def find_by_catalog_name(name: str, tenant_name: str):
    return TrinoCatalogMetaData.query.filter(
        TrinoCatalogMetaData.name == name,
        TrinoCatalogMetaData.tenant_name.in_(['*', tenant_name])
    ).first()


def update(catalog: TrinoCatalogMetaData):
    db.session.add(catalog)
    db.session.commit()


def delete(catalog: TrinoCatalogMetaData):
    db.session.delete(catalog)
    db.session.commit()


def delete_by_tenant_name(tenant_name: str):
    catalogs_to_delete = db.session.query(TrinoCatalogMetaData).filter_by(tenant_name=tenant_name).all()
    for catalog in catalogs_to_delete:
        db.session.delete(catalog)
    db.session.commit()
