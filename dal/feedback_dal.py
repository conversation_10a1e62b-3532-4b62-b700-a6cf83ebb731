from models.conversation_meta_data import db, UserFeedBack
from utils.PaginationUtils import Pageable
from models.enums import SortOrder


def create(feedback: UserFeedBack) -> None:
    db.session.add(feedback)
    db.session.commit()


def find_feedbacks_with_pagination_and_sorting(filters: dict, pageable: Pageable) -> (
        [UserFeedBack], int):
    query = UserFeedBack.query

    for field, value in filters.items():
        if hasattr(UserFeedBack, field):
            query = query.filter(getattr(UserFeedBack, field) == value)

    sort_field = getattr(UserFeedBack, pageable.sort_by, UserFeedBack.createdAt)
    if pageable.sort_order == SortOrder.DESC:
        query = query.order_by(sort_field.desc())
    else:
        query = query.order_by(sort_field.asc())

    total = query.count()
    feedbacks = query.paginate(page=pageable.page_number, per_page=pageable.page_size, error_out=False).items

    return feedbacks, total


def find_feedbacks_by_user_and_thread(user_id: str, tenant_name: str, conversation_id: str) -> [UserFeedBack]:
    return UserFeedBack.query.filter_by(user_id=user_id, tenant_name=tenant_name, conversation_id=conversation_id).all()
