from functools import lru_cache
from langchain_openai import OpenAIEmbeddings
from models.conversation_meta_data import db, FewShotExample
from config.app_config import Config

embedding_model = OpenAIEmbeddings(model=Config.OPEN_AI_EMBEDDINGS)


def create(example: FewShotExample):
    db.session.add(example)
    db.session.commit()


@lru_cache(maxsize=1000)
def get_cached_embedding(text: str):
    return tuple(embedding_model.embed_query(text))


def get_similar_few_shot_examples(user_question: str, top_n: int = 3) -> [FewShotExample]:
    user_question_embedding = get_cached_embedding(user_question)
    result = (db.session.query(FewShotExample)
              .order_by(FewShotExample.question_embedding.cosine_distance(user_question_embedding))
              .limit(top_n).all())
    return result
