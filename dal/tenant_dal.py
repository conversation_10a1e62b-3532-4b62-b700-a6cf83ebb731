from models.meta_data import db, TenantInfo


def create(tenant_meta_data: TenantInfo):
    db.session.add(tenant_meta_data)
    db.session.commit()


def delete_by_tenant_name(tenant_name: str):
    tenant = TenantInfo.query.filter_by(tenant_name=tenant_name).first()
    if tenant:
        db.session.delete(tenant)
        db.session.commit()


def find_by_tenant_name(tenant_name: str) -> TenantInfo:
    return TenantInfo.query.filter_by(tenant_name=tenant_name).first()
