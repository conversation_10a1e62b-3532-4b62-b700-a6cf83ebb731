from models.meta_data import db, TrinoDBMetaData


def create(db_meta_data: TrinoDBMetaData):
    db.session.add(db_meta_data)
    db.session.commit()


def find_all(tenant_name: str):
    return TrinoDBMetaData.query.filter(TrinoDBMetaData.tenant_name.in_(['*', tenant_name])).all()


def find_by_db_name(name: str, tenant_name: str):
    return TrinoDBMetaData.query.filter(
        TrinoDBMetaData.name == name,
        TrinoDBMetaData.tenant_name.in_(['*', tenant_name])
    ).first()


def update(db_meta_data: TrinoDBMetaData):
    db.session.add(db_meta_data)
    db.session.commit()


def delete(db_meta_data: TrinoDBMetaData):
    db.session.delete(db_meta_data)
    db.session.commit()


def find_by_catalog_name(trino_catalog_name: str, tenant_name: str):
    return TrinoDBMetaData.query.filter(
        TrinoDBMetaData.trino_catalog_name == trino_catalog_name,
        TrinoDBMetaData.tenant_name.in_(['*', tenant_name])
    ).all()
