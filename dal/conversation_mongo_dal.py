import difflib
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional

import pymongo
from pymongo import Mongo<PERSON>lient

from config.app_config import Config
from utils.LogUtils import LOG

client = MongoClient(Config.MONGO_URI)
mongo_db = client[Config.DATABASE_NAME]
mongo_collection = mongo_db[Config.MONGO_COLLECTION]
mongo_query_collection = mongo_db[Config.MONGO_HISTORY_COLLECTION]


class QuestionQueryManager:
    def __init__(self):
        """
        Initialize MongoDB connection and database
        """
        try:
            self.client = pymongo.MongoClient(Config.MONGO_URI)
            self.db = self.client[Config.DATABASE_NAME]
            self.questions_collection = self.db['question_query_history']
            self.questions_collection.create_index('question_text', unique=True)
        except pymongo.errors.ConnectionFailure as e:
            LOG.error(f"Could not connect to MongoDB: {e}")
            raise

    def is_similar(self, existing_queries: List[str], new_query: str, threshold: float = 0.85) -> bool:
        """Check if the new_query is too similar to existing queries."""
        for query in existing_queries:
            similarity = difflib.SequenceMatcher(None, query, new_query).ratio()
            if similarity >= threshold:
                return True  # Too similar, should not insert
        return False

    def upsert_question_with_queries(self, question: str, queries: List[str]) -> str:
        """Insert or update a question with queries while avoiding near-duplicates."""
        try:
            # Fetch existing queries for the question
            question = question.strip().lower()
            existing_entry = self.questions_collection.find_one({'question_text': question}, {'queries': 1})
            existing_queries = existing_entry.get('queries', []) if existing_entry else []

            # Filter out near-duplicates
            unique_queries = []
            for q in set(queries):  # Remove exact duplicates before checking similarity
                if not self.is_similar(existing_queries, q):
                    unique_queries.append(q)

            if not unique_queries:  # Nothing new to add
                return "No new queries added"

            # Perform the update
            result = self.questions_collection.update_one(
                {'question_text': question},
                {'$addToSet': {'queries': {'$each': unique_queries}},
                 '$setOnInsert': {'created_at': datetime.now()},
                 '$set': {'updated_at': datetime.now()}},
                upsert=True
            )

            return "Updated" if result.modified_count > 0 else "Inserted"
        except Exception as e:
            LOG.error(f"Error upserting question: {e}")
            return "Error"

    def get_queries_for_question(self, question: str, similarity_threshold: float = 0.8) -> Tuple[List[str], Optional[str]]:
        """Find queries for a question using similarity search."""
        all_questions = list(self.questions_collection.find({}, {'question_text': 1, 'queries': 1}))
        best_match, highest_similarity = None, 0

        for doc in all_questions:
            similarity = difflib.SequenceMatcher(None, question.lower(), doc['question_text'].lower()).ratio()
            if similarity > highest_similarity and similarity >= similarity_threshold:
                highest_similarity, best_match = similarity, doc

        if best_match:
            LOG.info(f"Best match (Similarity: {highest_similarity:.2f}): {best_match['question_text']}")
            return best_match['queries'][:5], best_match['question_text']

        LOG.info("No similar question found.")
        return [], None

    def delete_question(self, question: str) -> bool:
        """Delete a question document."""
        try:
            result = self.questions_collection.delete_one({'question_text': question})
            return result.deleted_count > 0
        except Exception as e:
            LOG.error(f"Error deleting question: {e}")
            return False

    def close_connection(self):
        """Close the MongoDB connection."""
        self.client.close()
