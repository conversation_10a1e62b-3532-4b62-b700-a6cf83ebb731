import uuid

from config.app_config import Config
from models.conversation_meta_data import db, Conversation
from utils.LogUtils import common_exception_handler
from utils.PaginationUtils import Pageable
from models.enums import SortOrder
from langchain_postgres import PostgresChatMessageHistory
import psycopg


@common_exception_handler
def create(conversation: Conversation) -> None:
    db.session.add(conversation)
    db.session.commit()


def find_all() -> [Conversation]:
    return Conversation.query.all()


def find_by_id(id: uuid) -> Conversation:
    return Conversation.query.get(id)


def find_by_user_and_tenant(user_id: str, tenant_name: str, pageable: Pageable) -> [Conversation]:
    query = Conversation.query.filter_by(user_id=user_id, tenant_name=tenant_name)

    if pageable.sort_order == SortOrder.ASC:
        query = query.order_by(getattr(Conversation, pageable.sort_by).asc())
    else:
        query = query.order_by(getattr(Conversation, pageable.sort_by).desc())

    total = query.count()
    conversations = query.offset((pageable.page_number - 1) * pageable.page_size).limit(pageable.page_size).all()
    return conversations, total


def update(con: Conversation) -> None:
    db.session.add(con)
    db.session.commit()


def delete(con: Conversation) -> None:
    db.session.delete(con)
    db.session.commit()


def get_thread_ids(user_id: str, tenant_name: str, excluded_ids: [str]) -> [str]:
    query = db.session.query(Conversation.id).filter(
        Conversation.user_id == user_id,
        Conversation.tenant_name == tenant_name,
        ~Conversation.id.in_(excluded_ids)
    )
    return [result[0] for result in query.all()]


def delete_conversations_not_in_excluded_ids(user_id: str, tenant_name: str, excluded_ids: [str]) -> None:
    db.session.query(Conversation).filter(
        Conversation.user_id == user_id,
        Conversation.tenant_name == tenant_name,
        ~Conversation.id.in_(excluded_ids)
    ).delete(synchronize_session=False)
    db.session.commit()


def get_chat_history(conversation_id: str):
    sync_connection = psycopg.connect(Config.SQLALCHEMY_DATABASE_URI)
    table_name = "Conversation_History"
    PostgresChatMessageHistory.create_tables(sync_connection, "Conversation_History")
    chat_history = PostgresChatMessageHistory(table_name,conversation_id,sync_connection=sync_connection)
    return chat_history
