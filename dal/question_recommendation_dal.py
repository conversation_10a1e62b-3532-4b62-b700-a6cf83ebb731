from sqlalchemy import asc, desc
from models.enums import SortOrder
from models.conversation_meta_data import db, UserQuestion, Conversation
from utils.LogUtils import LOG
from utils.PaginationUtils import Pageable


def find_all_recent_questions(user_id: str, module_identifier: str, pageable: Pageable, conversation_id: str = None,
                              entity_type: str = None) -> [UserQuestion]:
    """
        Fetch recent questions based on conversation ID, module identifier, or entity type with pagination.
    """
    query = db.session.query(UserQuestion).join(Conversation, Conversation.id == UserQuestion.conversation_id)
    # Apply filters for user_id and module_identifier
    query = query.filter(UserQuestion.user_id == user_id, UserQuestion.module_identifier == module_identifier)
    # Filter by conversation_id if provided
    if conversation_id:
        query = query.filter(UserQuestion.conversation_id == conversation_id)
    # Filter by entity_type if provided
    if entity_type:
        query = query.filter(Conversation.entity_type == entity_type)
    # Handle sorting and pagination
    sort_column = getattr(UserQuestion, pageable.sort_by, UserQuestion.created_on)
    sort_order = desc(sort_column) if pageable.sort_order == SortOrder.DESC else asc(sort_column)
    result = (
        query.order_by(sort_order)
        .offset((pageable.page_number - 1) * pageable.page_size)
        .limit(pageable.page_size)
        .all()
    )
    LOG.info(
        f"Fetched {len(result)} questions for user_id: {user_id}, module_identifier: {module_identifier}, entity_type: {entity_type}.")
    return result
