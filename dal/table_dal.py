from models.meta_data import db, TrinoTableMetaData


def create(table_meta_data: TrinoTableMetaData):
    db.session.add(table_meta_data)
    db.session.commit()


def find_all(tenant_name: str):
    return TrinoTableMetaData.query.filter(TrinoTableMetaData.tenant_name.in_(['*', tenant_name])).all()


def find_by_table_name(name: str, tenant_name: str):
    return TrinoTableMetaData.query.filter(
        TrinoTableMetaData.name == name,
        TrinoTableMetaData.tenant_name.in_(['*', tenant_name])
    ).first()


def update(table_meta_data: TrinoTableMetaData):
    db.session.add(table_meta_data)
    db.session.commit()


def delete(table_meta_data: TrinoTableMetaData):
    db.session.delete(table_meta_data)
    db.session.commit()


def find_by_db_name(trino_db_name: str, tenant_name: str):
    return TrinoTableMetaData.query.filter(
        TrinoTableMetaData.trino_db_name == trino_db_name,
        TrinoTableMetaData.tenant_name.in_(['*', tenant_name])
    ).all()
