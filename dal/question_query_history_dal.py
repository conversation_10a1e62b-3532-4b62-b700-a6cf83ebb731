from models.conversation_meta_data import db, QuestionQueryHistory
from typing import List, Tuple, Optional
import difflib
from utils.LogUtils import LOG


def create(question_query_history: QuestionQueryHistory) -> None:
    """Create a new question query history entry."""
    db.session.add(question_query_history)
    db.session.commit()


def create_question_query_entry(question: str, queries: List[str]) -> None:
    """
    Create a new question query history entry.

    Args:
        question (str): The question text
        queries (List[str]): List of SQL queries associated with the question
    """
    try:
        entry = QuestionQueryHistory(question_text=question, queries=queries)
        db.session.add(entry)
        db.session.commit()
        LOG.info(f"Question query entry created successfully for question: {question[:50]}...")
    except Exception as e:
        LOG.error(f"Error creating question query entry: {e}")
        db.session.rollback()
        raise


def find_all(limit: int = 100) -> List[QuestionQueryHistory]:
    """Get all question query history entries."""
    return QuestionQueryHistory.query.order_by(QuestionQueryHistory.created_at.desc()).limit(limit).all()


def find_by_question(question: str) -> Optional[QuestionQueryHistory]:
    """Find question query history entry by question text."""
    return QuestionQueryHistory.query.filter_by(question_text=question).first()


def get_queries_by_question(question: str) -> List[str]:
    """
    Get queries associated with a specific question.

    Args:
        question (str): The question text to search for

    Returns:
        List[str]: List of queries associated with the question
    """
    try:
        entry = find_by_question(question)
        return entry.queries if entry else []
    except Exception as e:
        LOG.error(f"Error retrieving queries for question: {e}")
        return []


def get_all_question_query_pairs(limit: int = 100) -> List[QuestionQueryHistory]:
    """
    Get all question-query pairs from the database.

    Args:
        limit (int): Maximum number of entries to return

    Returns:
        List[QuestionQueryHistory]: List of question query history entries
    """
    try:
        return find_all(limit)
    except Exception as e:
        LOG.error(f"Error retrieving question query pairs: {e}")
        return []


def is_similar(existing_queries: List[str], new_query: str, threshold: float = 0.85) -> bool:
    """Check if the new_query is too similar to existing queries."""
    for query in existing_queries:
        similarity = difflib.SequenceMatcher(None, query, new_query).ratio()
        if similarity >= threshold:
            return True  # Too similar, should not insert
    return False


def upsert_question_with_queries(question: str, queries: List[str]) -> str:
    """Insert or update a question with queries while avoiding near-duplicates."""
    try:
        # Fetch existing queries for the question
        question = question.strip().lower()
        existing_entry = find_by_question(question)
        existing_queries = existing_entry.queries if existing_entry else []

        # Filter out near-duplicates
        unique_queries = []
        for q in set(queries):  # Remove exact duplicates before checking similarity
            if not is_similar(existing_queries, q):
                unique_queries.append(q)

        if not unique_queries:  # Nothing new to add
            return "No new queries added"

        if existing_entry:
            # Update existing entry by adding unique queries
            all_queries = list(set(existing_queries + unique_queries))
            existing_entry.queries = all_queries
            db.session.commit()
            return "Updated"
        else:
            # Create new entry
            new_entry = QuestionQueryHistory(question_text=question, queries=unique_queries)
            db.session.add(new_entry)
            db.session.commit()
            return "Inserted"

    except Exception as e:
        LOG.error(f"Error upserting question: {e}")
        db.session.rollback()
        return "Error"


def get_queries_for_question(question: str, similarity_threshold: float = 0.8) -> Tuple[List[str], Optional[str]]:
    """Find queries for a question using similarity search."""
    try:
        all_questions = find_all()
        best_match, highest_similarity = None, 0

        for entry in all_questions:
            similarity = difflib.SequenceMatcher(None, question.lower(), entry.question_text.lower()).ratio()
            if similarity > highest_similarity and similarity >= similarity_threshold:
                highest_similarity, best_match = similarity, entry

        if best_match:
            LOG.info(f"Best match (Similarity: {highest_similarity:.2f}): {best_match.question_text}")
            return best_match.queries[:5], best_match.question_text

        LOG.info("No similar question found.")
        return [], None

    except Exception as e:
        LOG.error(f"Error finding queries for question: {e}")
        return [], None


def delete_question(question: str) -> bool:
    """Delete a question entry."""
    try:
        entry = find_by_question(question)
        if entry:
            db.session.delete(entry)
            db.session.commit()
            return True
        return False
    except Exception as e:
        LOG.error(f"Error deleting question: {e}")
        db.session.rollback()
        return False


def delete(question_query_history: QuestionQueryHistory) -> None:
    """Delete a question query history entry."""
    db.session.delete(question_query_history)
    db.session.commit()


def close_db_session() -> bool:
    """
    Close the current database session.

    Returns:
        bool: True if session closed successfully, False otherwise
    """
    try:
        if db.session:
            db.session.close()
            LOG.info("Database session closed successfully")
            return True
        else:
            LOG.info("No active database session to close")
            return True
    except Exception as e:
        LOG.error(f"Error closing database session: {e}")
        return False


def cleanup_db_connections() -> bool:
    """
    Perform comprehensive database cleanup.
    Closes sessions and disposes of connection pools.

    Returns:
        bool: True if cleanup successful, False otherwise
    """
    try:
        # Close current session
        if db.session:
            db.session.close()
            LOG.info("Database session closed")

        # Dispose of connection pool to free up connections
        if db.engine:
            db.engine.dispose()
            LOG.info("Database engine connection pool disposed")

        return True
    except Exception as e:
        LOG.error(f"Error during database cleanup: {e}")
        return False


# For backward compatibility - create a simple class that uses the functions
class QuestionQueryManager:
    """Backward compatibility class that delegates to module functions."""

    def is_similar(self, existing_queries: List[str], new_query: str, threshold: float = 0.85) -> bool:
        return is_similar(existing_queries, new_query, threshold)

    def upsert_question_with_queries(self, question: str, queries: List[str]) -> str:
        return upsert_question_with_queries(question, queries)

    def get_queries_for_question(self, question: str, similarity_threshold: float = 0.8) -> Tuple[List[str], Optional[str]]:
        return get_queries_for_question(question, similarity_threshold)

    def delete_question(self, question: str) -> bool:
        return delete_question(question)

    def close_connection(self) -> bool:
        return close_db_session()

    def cleanup_connections(self) -> bool:
        return cleanup_db_connections()