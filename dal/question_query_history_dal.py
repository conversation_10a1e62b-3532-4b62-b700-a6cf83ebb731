from models.conversation_meta_data import db, QuestionQueryHistory
from typing import List, Tuple, Optional
import difflib
from utils.LogUtils import LOG


def is_similar(existing_queries: List[str], new_query: str, threshold: float = 0.85) -> bool:
    """Check if the new_query is too similar to existing queries."""
    for query in existing_queries:
        similarity = difflib.SequenceMatcher(None, query, new_query).ratio()
        if similarity >= threshold:
            return True  # Too similar, should not insert
    return False


def upsert_question_with_queries(question: str, queries: List[str]) -> str:
    """Insert or update a question with queries while avoiding near-duplicates."""
    try:
        # Fetch existing queries for the question
        question = question.strip().lower()
        existing_entry = db.session.query(QuestionQueryHistory).filter_by(question_text=question).first()
        existing_queries = existing_entry.queries if existing_entry else []

        # Filter out near-duplicates
        unique_queries = []
        for q in set(queries):  # Remove exact duplicates before checking similarity
            if not is_similar(existing_queries, q):
                unique_queries.append(q)

        if not unique_queries:  # Nothing new to add
            return "No new queries added"

        if existing_entry:
            # Update existing entry by adding unique queries
            all_queries = list(set(existing_queries + unique_queries))
            existing_entry.queries = all_queries
            db.session.commit()
            return "Updated"
        else:
            # Create new entry
            new_entry = QuestionQueryHistory(question_text=question, queries=unique_queries)
            db.session.add(new_entry)
            db.session.commit()
            return "Inserted"

    except Exception as e:
        LOG.error(f"Error upserting question: {e}")
        db.session.rollback()
        return "Error"


def get_queries_for_question(question: str, similarity_threshold: float = 0.8) -> Tuple[List[str], Optional[str]]:
    """Find queries for a question using similarity search."""
    try:
        all_questions = db.session.query(QuestionQueryHistory).all()
        best_match, highest_similarity = None, 0

        for entry in all_questions:
            similarity = difflib.SequenceMatcher(None, question.lower(), entry.question_text.lower()).ratio()
            if similarity > highest_similarity and similarity >= similarity_threshold:
                highest_similarity, best_match = similarity, entry

        if best_match:
            LOG.info(f"Best match (Similarity: {highest_similarity:.2f}): {best_match.question_text}")
            return best_match.queries[:5], best_match.question_text

        LOG.info("No similar question found.")
        return [], None

    except Exception as e:
        LOG.error(f"Error finding queries for question: {e}")
        return [], None


def delete_question(question: str) -> bool:
    """Delete a question entry."""
    try:
        entry = db.session.query(QuestionQueryHistory).filter_by(question_text=question).first()
        if entry:
            db.session.delete(entry)
            db.session.commit()
            return True
        return False
    except Exception as e:
        LOG.error(f"Error deleting question: {e}")
        db.session.rollback()
        return False


def close_connection() -> bool:
    """
    Close the database session.

    Returns:
        bool: True if connection closed successfully, False otherwise
    """
    try:
        if db.session:
            db.session.close()
            LOG.info("Database session closed successfully")
            return True
        else:
            LOG.info("No active database session to close")
            return True
    except Exception as e:
        LOG.error(f"Error closing database session: {e}")
        return False


