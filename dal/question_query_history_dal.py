from models.conversation_meta_data import db, QuestionQueryHistory
from typing import List


def create_question_query_entry(question: str, queries: List[str]) -> None:
    entry = QuestionQueryHistory(question_text=question, queries=queries)
    db.session.add(entry)
    db.session.commit()


def get_queries_by_question(question: str) -> List[str]:
    entry = db.session.query(QuestionQueryHistory).filter_by(question_text=question).first()
    return entry.queries if entry else []


def get_all_question_query_pairs(limit: int = 100) -> List[QuestionQueryHistory]:
    return db.session.query(QuestionQueryHistory).order_by(QuestionQueryHistory.created_at.desc()).limit(limit).all()


class QuestionQueryManager:
    pass