from datetime import datetime
import json

from bson import ObjectId

from utils.LogUtils import LOG


def format_llm_response_to_json(resp):
    LOG.info(f"Attempting to convert response to json = {resp}")

    resp = resp.replace("```json", "")
    resp = resp.replace("```tabular-data-json", "")
    resp = resp.replace("```", "")
    return json.loads(resp)


def json_serializable(data):
    # Define a custom JSON encoder function to handle the ObjectId and datetime types
    if isinstance(data, ObjectId):
        # Convert ObjectId to a string
        return str(data)
    elif isinstance(data, datetime):
        # Convert datetime to ISO 8601 string
        return data.isoformat()
    elif isinstance(data, dict):
        # Recursively convert each value in the dictionary
        return {key: json_serializable(value) for key, value in data.items()}
    elif isinstance(data, list):
        # Recursively convert each element in the list
        return [json_serializable(item) for item in data]
    else:
        # For all other types, return the data as is
        return data

