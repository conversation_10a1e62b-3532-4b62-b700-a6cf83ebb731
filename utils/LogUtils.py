import os
import sys
from loguru import logger
import warnings
warnings.filterwarnings("ignore")

ENV = os.getenv("ENV", "DEV")  # Default to 'development' if ENV is not set

# Remove default logger configuration
logger.remove()

# Customize logger format
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <cyan>{extra[env]}</cyan> | <level>{level: <5} | {message} </level>",
    colorize=True  # Enable color output
)

logger = logger.bind(env=ENV)


class CSLogger:
    @staticmethod
    def debug(msg, *args, **kwargs):
        logger.debug(msg, *args, **kwargs)

    @staticmethod
    def info(msg, *args, **kwargs):
        logger.info(msg, *args, **kwargs)

    @staticmethod
    def warning(msg, *args, **kwargs):
        logger.warning(msg, *args, **kwargs)

    @staticmethod
    def error(msg, *args, **kwargs):
        logger.error(msg, *args, **kwargs)

    @staticmethod
    def critical(msg, *args, **kwargs):
        logger.critical(msg, *args, **kwargs)

    @staticmethod
    def exception(msg, *args, **kwargs):
        logger.exception(msg, *args, **kwargs)

    @staticmethod
    def catch(msg, *args, **kwargs):
        logger.catch(msg, *args, **kwargs)

    @staticmethod
    def success(msg, *args, **kwargs):
        logger.success(msg, *args, **kwargs)

    @staticmethod
    def err(msg, *args, **kwargs):
        logger.error(msg, *args, **kwargs)

    @staticmethod
    def warn(msg, *args, **kwargs):
        logger.warning(msg, *args, **kwargs)

    @staticmethod
    def trace(msg, *args, **kwargs):
        logger.trace(msg, *args, **kwargs)


# utils/LogUtils.py
LOG = CSLogger()


def common_exception_handler(func):
    """
    A decorator to wrap functions with a common exception handler.
    It will print the function name and the error message if an exception occurs.
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Get the name of the function where the exception occurred
            func_name = func.__name__
            print(f"Error in function '{func_name}': {str(e)}")
    return wrapper
