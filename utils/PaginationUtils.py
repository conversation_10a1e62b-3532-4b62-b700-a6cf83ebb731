from dataclasses import dataclass
from models.enums import SortOrder
from utils.LogUtils import LOG


@dataclass
class Pageable:
    page_number: int = 1
    page_size: int = 10
    sort_by: str = 'createdAt'
    sort_order: SortOrder = SortOrder.DESC


def get_pageable_from_request(request_args):
    """
    Extracts and validates pagination and sorting parameters from the request arguments.
    Returns a Pageable object or raises a ValueError for invalid inputs.
    """
    try:
        page_number = int(request_args.get('pageNumber', 1))
        page_size = int(request_args.get('pageSize', 10))
        sort_by = request_args.get('sortBy', 'createdAt')
        sort_order = request_args.get('sortOrder', 'DESC').upper()

        if page_number <= 0 or page_size <= 0:
            LOG.error(f"Invalid pagination parameters: pageNumber={page_number}, pageSize={page_size}")
            raise ValueError('Page number and page size must be positive and greater than zero.')

        if sort_order not in SortOrder.__members__:
            LOG.error(f"Invalid sort order: {sort_order}")
            allowed_values = ', '.join(SortOrder.__members__.keys())
            raise ValueError(f"Invalid sort order. Allowed values are {allowed_values}.")

        sort_order_enum = SortOrder[sort_order]

        pageable = Pageable(
            page_number=page_number,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order_enum
        )
        LOG.info(f"Generated pageable: {pageable}")
        return pageable
    except ValueError as e:
        LOG.error(f"Error generating pageable: {e}")
        raise
