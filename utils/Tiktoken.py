import tiktoken

from constants.app_constants import ENCODING_NAME
from utils.LogUtils import LOG
import time
from functools import wraps


# Count the number of tokens in the query result using tiktoken.
def get_tokens(data: str, model: str):
    query_tokens = compute_tokens_using_tiktoken(data, model)
    LOG.info(f"Computed token count: {query_tokens}")
    return query_tokens


# Computes the number of tokens in a text using tiktoken for a specific model.
def compute_tokens_using_tiktoken(text: str, model: str) -> int:
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        LOG.warning(f"Model not found: {model}. Using {ENCODING_NAME} as fallback encoding.")
        encoding = tiktoken.get_encoding(ENCODING_NAME)
    return len(encoding.encode(text))


def execution_timer(func):
    """
    A decorator that measures and prints the execution time of a function.
    Args:
        func: The function to be decorated
    Returns:
        Wrapper function that executes the original function and prints its execution time
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Record the start time
        start_time = time.time()

        # Execute the function
        result = func(*args, **kwargs)

        # Record the end time
        end_time = time.time()

        # Calculate and print the execution time
        execution_time = end_time - start_time
        LOG.info(f"Function '{func.__name__}' took {execution_time:.6f} seconds to execute")

        return result
    return wrapper
