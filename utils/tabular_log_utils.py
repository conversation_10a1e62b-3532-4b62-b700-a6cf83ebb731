from tabulate import tabulate

from utils.LogUtils import LOG


def log_tabular_data(prefix, headers, data):
    """
    Logs data in a tabular format with a specified prefix.

    Args:
        prefix (str): The prefix for the log message.
        headers (list): The headers for the table.
        data (list): The data to be logged in table format.
        log_level (int): The logging level (default is logging.INFO).
    """
    table_data = [headers] + data
    table_string = tabulate(table_data, headers="firstrow", tablefmt="grid")
    log_message = f"{prefix}\n{table_string}"
    LOG.info(log_message)
