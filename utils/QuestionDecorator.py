from datetime import datetime

from langchain_core.messages import HumanMessage

import dal.entity_metadata_dal as entity_metadata_dal
from config.app_config import Config
from constants.question_metadata_prompt import QUESTION_METADATA_PROMPT
from dal.conversation_dal import get_chat_history
from models.conversation_meta_data import Conversation
from models.meta_data import TenantInfo
from utils.LogUtils import LOG
import dal.conversation_dal as conversation_dal


def decorate_question(conversation: Conversation):
    if not conversation.entity_type or not conversation.entity_id or not conversation.entity_name:
        question_metadata = f"This conversation is for module={conversation.module_identifier} "
    else:
        entity = entity_metadata_dal.find_by_entity_type(conversation.entity_type)
        if entity:
            entity_context = {
                "entity_type": entity.entity_type,
                "entity_id": entity.entity_id,
                "entity_name": entity.entity_name,
            }
        else:
            entity_context = {}

        question_metadata = QUESTION_METADATA_PROMPT.format(
            entity_context=entity_context,
            module_identifier=conversation.module_identifier,
            entity_type=conversation.entity_type,
            entity_id=conversation.entity_id,
            entity_name=conversation.entity_name
        )
    return question_metadata


def save_first_conversation_message(conversation: Conversation, question: str, tenant: TenantInfo, conversation_id):
    if not conversation.first_message:
        LOG.info("Saving the first message")
        conversation.first_message = question
        conversation_dal.update(conversation)
        question_metadata = f"[Tenant name = {tenant.tenant_name} and Tenant ID = {tenant.tenant_id}] + QUESTION_METADATA = {decorate_question(conversation)} =>"
        chat_message_history = get_chat_history(conversation_id)
        chat_message_history.add_user_message(HumanMessage(content=f'{question_metadata} {question}', additional_kwargs={"timestamp": datetime.now().strftime("%H:%M:%S"), "skip": True}))
    else:
        question_metadata = f"[Tenant name = {tenant.tenant_name} and Tenant ID = {tenant.tenant_id}] =>"

    conversation.lastUpdatedAt = datetime.utcnow()
    conversation_dal.update(conversation)
    return question_metadata


def convert_messages(message_list):
    formatted_messages = []
    for message in reversed(message_list):
        role = "user" if isinstance(message, HumanMessage) else "assistant"
        text = message.content
        timestamp = message.additional_kwargs.get("timestamp", int(datetime.now().timestamp()))
        skip_flag = message.additional_kwargs.get("skip", False)
        if not skip_flag:
            formatted_messages.append({
                "role": role,
                "text": text,
                "timestamp": timestamp
            })

    return formatted_messages

