from flask import jsonify, request
from werkzeug.exceptions import HTTPException
from datetime import datetime


class APIError(Exception):
    """Custom Exception class for API errors."""

    def __init__(self, message, status_code=400):
        super().__init__(message)
        self.message = message
        self.status_code = status_code


def register_error_handlers(app):
    @app.errorhandler(Exception)
    def handle_exception(e):
        # Common error response structure
        error_response = {
            "path": request.path,
            "timestamp": datetime.utcnow().isoformat(),
        }

        # Handle HTTP exceptions with a status code
        if isinstance(e, HTTPException):
            error_response.update({
                "error": e.name,
                "message": e.description,
            })
            return jsonify(error_response), e.code

        # Handle API custom exceptions
        if isinstance(e, APIError):
            error_response.update({
                "error": "API Error",
                "message": e.message,
            })
            return jsonify(error_response), e.status_code

        # Handle generic Python exceptions
        error_response.update({
            "error": "Internal Server Error",
            "message": "An unexpected error occurred.",
        })
        return jsonify(error_response), 500
