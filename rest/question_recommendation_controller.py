from flasgger import swag_from
from flask import request, jsonify

import service.question_recommendation_service as question_recommendation_service
from interceptor.thread_context import ThreadLocalContext
from utils.global_exception_handler import APIError
from . import api_v1


@api_v1.route('/question-recommendations', methods=['GET'])
@swag_from({
    'tags': ['UserQuestions'],
    'summary': 'Get Question Recommendations for Ongoing Chat',
    'description': '<PERSON><PERSON> recommended questions for an ongoing chat session based on the conversation history.',
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user whose conversations will be cleared'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The name of the tenant'
        },
        {
            'name': 'module_identifier',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'Identifier for the module (e.g., event-timeline).'
        },
        {
            'name': 'conversation_id',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'The ID of the conversation'
        }
    ],
    'responses': {
        200: {
            'description': 'Successfully retrieved recommended questions.',
            'examples': {
                'application/json': {
                    "recommended_questions": [
                        "What is the definition of AI?",
                        "How does machine learning work?",
                        "What are neural networks?"
                    ]
                }
            }
        },
        400: {
            'description': 'Missing required parameters.',
            'examples': {
                'application/json': {
                    "error": "module_identifier and conversation_id are required"
                }
            }
        },
        500: {
            'description': 'Internal server error occurred.',
            'examples': {
                'application/json': {
                    "error": "An unexpected error occurred."
                }
            }
        }
    }
})
def question_recommendations():
    """
        Endpoint to fetch recommended questions for a conversation.
        Either conversation_id or module_identifier must be provided.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()

        module_identifier = request.args.get('module_identifier')
        entity_type = request.args.get('entity_type')
        conversation_id = request.args.get('conversation_id')

        if not module_identifier:
            raise APIError("module_identifier is required for question recommendations")

        recommendations = question_recommendation_service.get_recommended_questions_for_chat(user_id, module_identifier,
                                                                                             conversation_id,
                                                                                             entity_type)
        return jsonify({"recommended_questions": recommendations}), 200
    except Exception as e:
        raise APIError(str(e), 500)
