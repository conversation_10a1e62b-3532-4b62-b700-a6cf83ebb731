from flasgger import swag_from
from flask import request, jsonify

import service.feedback_service as feedback_service
from interceptor.thread_context import ThreadLocalContext
from models.conversation_meta_data import UserFeedbackSchema, db
from utils.PaginationUtils import get_pageable_from_request
from utils.global_exception_handler import APIError
from . import api_v1

feedback_schema = UserFeedbackSchema()
feedbacks_schema = UserFeedbackSchema(many=True)


@api_v1.route('conversations/<conversation_id>/feedbacks', methods=['POST'])
@swag_from({
    'tags': ['Feedback'],
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user submitting the feedback'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the user'
        },
        {
            'name': 'conversation_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the conversation'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/UserFeedBack'
            }
        }
    ],
    'responses': {
        '201': {
            'description': 'Feedback submitted successfully',
            'schema': {
                'type': 'object',
                'properties': {
                    'message': {'type': 'string', 'example': 'Feedback submitted successfully'},
                    'feedback': {
                        'type': 'string',
                        'description': 'The ID of the submitted feedback',
                        'example': '123e4567-e89b-12d3-a456-426614174000'
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid input provided',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string'}
                }
            }
        }
    }
})
def submit_feedback(conversation_id):
    """
        Submit feedback for a specific conversation.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        data = request.get_json()

        if not data:
            raise APIError("Invalid input. Feedback data is required.", 400)
        if not conversation_id:
            raise APIError("Invalid input. conversation_id is required.", 400)

        try:
            data['user_id'] = user_id
            data['tenant_name'] = tenant_name
            data['conversation_id'] = conversation_id
            feedback_data = feedback_schema.load(data, session=db.session)
        except ValueError as e:
            raise APIError(str(e), 400)

        feedback = feedback_service.submit_feedback(feedback_data)
        return jsonify({'message': 'Feedback submitted successfully', 'feedback': feedback.id}), 201
    except ValueError as e:
        raise APIError(str(e), 400)


@api_v1.route('/feedbacks', methods=['GET'])
@swag_from({
    'tags': ['Feedback'],
    'parameters': [
        {
            'name': 'filters',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'Optional query parameters to filter feedbacks. Example: user_id=admin@dev&tenant_name=dev'
        },
        {
            'name': 'pageNumber',
            'in': 'query',
            'type': 'integer',
            'required': False,
            'description': 'Page number for pagination. Defaults to 1.'
        },
        {
            'name': 'pageSize',
            'in': 'query',
            'type': 'integer',
            'required': False,
            'description': 'Number of items per page for pagination. Defaults to 10.'
        },
        {
            'name': 'sortBy',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'Field to sort by. Defaults to "createdAt".'
        },
        {
            'name': 'sortOrder',
            'in': 'query',
            'type': 'string',
            'enum': ['asc', 'desc'],
            'required': False,
            'description': 'Sort order. Defaults to "desc".'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns feedbacks matching the filters',
            'schema': {
                'type': 'object',
                'properties': {
                    'items': {
                        'type': 'array',
                        'items': {
                            '$ref': '#/definitions/Feedback'
                        }
                    },
                    'total': {'type': 'integer'},
                    'pageNumber': {'type': 'integer'},
                    'pageSize': {'type': 'integer'}
                }
            }
        },
        '404': {
            'description': 'No feedback found for the given filters'
        }
    }
})
def get_all_feedback_of_user():
    """
    Retrieve feedback based on optional pagination parameters and sorting.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        filters = request.args.to_dict()
        pageable = get_pageable_from_request(request.args)

        feedbacks, total = feedback_service.find_feedbacks_by_filters(
           filters, pageable )

        response = {
            "data": feedbacks_schema.dump(feedbacks, many=True),
            "total": total
        }
        return jsonify(response)
    except ValueError as e:
        raise APIError(str(e), 400)

@api_v1.route('conversations/<conversation_id>/feedbacks', methods=['GET'])
@swag_from({
    'tags': ['Feedback'],
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the user'
        },
        {
            'name': 'conversation_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the conversation'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns all feedback for the specified conversation and user',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/Feedback'
                }
            }
        },
        '404': {
            'description': 'No feedback found for the specified user and conversation'
        }
    }
})
def get_all_feedback_of_user_and_thread(conversation_id):
    """
        Retrieve all feedback submitted by the user for a specific conversation under the tenant.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        if not conversation_id:
            raise APIError("Invalid input. conversation_id is required.", 400)

        feedbacks = feedback_service.find_feedbacks_by_user_and_thread(user_id, tenant_name, conversation_id)
        if not feedbacks:
            raise APIError("No feedback found", 404)
        return feedbacks_schema.jsonify(feedbacks)
    except ValueError as e:
        raise APIError(str(e), 400)