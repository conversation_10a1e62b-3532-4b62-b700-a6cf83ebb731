from flask import request, jsonify

import service.db_service as db_service
import service.table_service as table_service
import service.column_service as column_service
from interceptor.thread_context import ThreadLocalContext

from models.meta_data import *
from utils.global_exception_handler import APIError
from . import api_v1
from flasgger import swag_from

db_schema = TrinoDBMetaDataSchema()
dbs_schema = TrinoDBMetaDataSchema(many=True)


@api_v1.route('/catalogs/<catalog_name>/databases', methods=['GET'])
@swag_from({
    'tags': ['Databases'],
    'parameters': [
        {
            'name': 'catalog_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the catalog whose databases are being retrieved'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the catalog'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns a list of databases for the specified catalog',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/DBMetaData'
                }
            }
        },
        '404': {
            'description': 'Catalog not found'
        }
    }
})
def get_databases_by_catalog(catalog_name):
    """
    Retrieve a list of databases associated with the specified catalog.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    databases = db_service.find_by_catalog_name(catalog_name, tenant_name)
    return dbs_schema.jsonify(databases)


@api_v1.route('/databases', methods=['POST'])
@swag_from({
    'tags': ['Databases'],
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the user'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/DBMetaData'
            }
        }
    ],
    'responses': {
        '201': {
            'description': 'Successfully created a new database',
            'schema': {
                '$ref': '#/definitions/DBMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        }
    }
})
def create_database():
    """
    Create a new database in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Db data is required.", 400)

    try:
        db_meta = db_schema.load(data, session=db.session)
    except ValueError as e:
        raise APIError(str(e), 400)

    db_meta = db_service.create(db_meta)
    return db_schema.jsonify(db_meta), 201


@api_v1.route('/databases/<db_name>', methods=['GET'])
@swag_from({
    'tags': ['Databases'],
    'parameters': [
        {
            'name': 'db_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the database to retrieve'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the database'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns details of the specified database',
            'schema': {
                '$ref': '#/definitions/DBMetaData'
            }
        },
        '404': {
            'description': 'Database not found'
        }
    }
})
def get_database_by_name(db_name):
    """
    Retrieve details of a specific database using its unique name.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    db_meta = db_service.find_by_db_name(db_name, tenant_name)
    if db_meta is None:
        raise APIError("DBMetaData not found", 404)
    return db_schema.jsonify(db_meta)


@api_v1.route('/databases', methods=['GET'])
@swag_from({
    'tags': ['Databases'],
    'parameters': [
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name to filter databases'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns a list of all databases in the system',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/DBMetaData'
                }
            }
        }
    }
})
def get_databases():
    """
    Retrieve a list of all databases in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    dbs = db_service.find_all(tenant_name)
    return dbs_schema.jsonify(dbs)


@api_v1.route('/databases/<db_name>', methods=['PUT'])
@swag_from({
    'tags': ['Databases'],
    'parameters': [
        {
            'name': 'db_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the database to be updated'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/DBMetaData'
            }
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the database'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully updated the database',
            'schema': {
                '$ref': '#/definitions/DBMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        },
        '404': {
            'description': 'Database not found'
        }
    }
})
def update_database(db_name):
    """
    Update an existing database's metadata.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Db data is required.", 400)

    existing = db_service.find_by_db_name(db_name, tenant_name)
    if existing is None:
        raise APIError("DBMetaData not found", 404)

    try:
        for key, value in data.items():
            setattr(existing, key, value)
    except ValueError as e:
        raise APIError(str(e), 400)

    db_meta = db_service.update(existing)
    return db_schema.jsonify(db_meta)


@api_v1.route('/databases/<db_name>', methods=['DELETE'])
@swag_from({
    'tags': ['Databases'],
    'parameters': [
        {
            'name': 'db_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the database to be deleted'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the database'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully deleted the database'
        },
        '404': {
            'description': 'Database not found'
        }
    }
})
def delete_database(db_name):
    """
    Delete a database using its unique name.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    existing = db_service.find_by_db_name(db_name, tenant_name)
    if existing is None:
        raise APIError("DBMetaData not found", 404)

    tables = table_service.find_by_db_name(db_name, tenant_name)

    for table in tables:
        columns = column_service.find_by_table_name(table.name, table.tenant_name)
        for column in columns:
            column_service.delete(column)
        table_service.delete(table)

    db_service.delete(existing)
    return jsonify({"message": "DBMetaData deleted successfully"})
