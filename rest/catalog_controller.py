from flasgger import swag_from
from flask import request, jsonify

import service.column_service as column_service
import service.db_service as db_service
import service.catalog_service as catalog_service
import service.table_service as table_service
from interceptor.thread_context import ThreadLocalContext
from models.meta_data import *
from utils.global_exception_handler import APIError
from . import api_v1

catalog_schema = TrinoCatalogMetaDataSchema()
catalogs_schema = TrinoCatalogMetaDataSchema(many=True)


@api_v1.route('/catalogs', methods=['POST'])
@swag_from({
    'tags': ['Catalogs'],
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the catalog.'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/CatalogMetaData'
            }
        }
    ],
    'responses': {
        '201': {
            'description': 'Successfully created a new catalog',
            'schema': {
                '$ref': '#/definitions/CatalogMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        }
    }
})
def create_catalog():
    """
    Create a new catalog in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Catalog data is required.", 400)

    try:
        catalog = catalog_schema.load(data, session=db.session)
    except ValueError as e:
        raise APIError(str(e), 400)

    catalog = catalog_service.create(catalog)
    return catalog_schema.jsonify(catalog), 201


@api_v1.route('/catalogs/<catalog_name>', methods=['GET'])
@swag_from({
    'tags': ['Catalogs'],
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        },
        {
            'name': 'catalog_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the catalog to retrieve'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the catalog'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns details of the specified catalog',
            'schema': {
                '$ref': '#/definitions/CatalogMetaData'
            }
        },
        '404': {
            'description': 'Catalog not found'
        }
    }
})
def get_catalog(catalog_name):
    """
    Retrieve details of a specific catalog using its unique name.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    catalog = catalog_service.find_by_catalog_name(catalog_name, tenant_name)
    if catalog is None:
        raise APIError("CatalogMetaData not found", 404)

    return catalog_schema.jsonify(catalog)


@api_v1.route('/catalogs', methods=['GET'])
@swag_from({
    'tags': ['Catalogs'],
    'parameters': [
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name to filter catalogs'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns a list of all catalogs in the system',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/CatalogMetaData'
                }
            }
        }
    }
})
def get_catalogs():
    """
    Retrieve a list of all catalogs in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    catalogs = catalog_service.find_all(tenant_name)
    return catalogs_schema.jsonify(catalogs)


@api_v1.route('/catalogs/<catalog_name>', methods=['PUT'])
@swag_from({
    'tags': ['Catalogs'],
    'parameters': [
        {
            'name': 'catalog_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the catalog to be updated'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/CatalogMetaData'
            }
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the catalog'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully updated the catalog',
            'schema': {
                '$ref': '#/definitions/CatalogMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        },
        '404': {
            'description': 'Catalog not found'
        }
    }
})
def update_catalog(catalog_name):
    """
    Update an existing catalog's metadata.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Catalog data is required.", 400)

    existing = catalog_service.find_by_catalog_name(catalog_name, tenant_name)
    if existing is None:
        raise APIError("CatalogMetaData not found", 404)

    try:
        for key, value in data.items():
            setattr(existing, key, value)
    except ValueError as e:
        raise APIError(str(e), 400)

    catalog = catalog_service.update(existing)
    return catalog_schema.jsonify(catalog)


@api_v1.route('/catalogs/<catalog_name>', methods=['DELETE'])
@swag_from({
    'tags': ['Catalogs'],
    'parameters': [
        {
            'name': 'catalog_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the catalog to be deleted'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the catalog'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully deleted the catalog'
        },
        '404': {
            'description': 'Catalog not found'
        }
    }
})
def delete_catalog(catalog_name):
    """
    Delete a catalog using its unique name.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    existing = catalog_service.find_by_catalog_name(catalog_name, tenant_name)
    if existing is None:
        raise APIError("CatalogMetaData not found", 404)

    databases = db_service.find_by_catalog_name(catalog_name, tenant_name)

    for database in databases:
        tables = table_service.find_by_db_name(database.name, database.tenant_name)
        for table in tables:
            columns = column_service.find_by_table_name(table.name, table.tenant_name)
            for column in columns:
                column_service.delete(column)
            table_service.delete(table)
        db_service.delete(database)

    catalog_service.delete(existing)
    return jsonify({"message": "CatalogMetaData deleted successfully"})
