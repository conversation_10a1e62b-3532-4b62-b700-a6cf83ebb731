from flask import request, jsonify
from service import metadata_service
from service import metadata_loader_service
from utils.global_exception_handler import APIError
from utils.LogUtils import LOG
from . import api_v1
from migration.tenant_config import TenantConfig


@api_v1.route('/metadata', methods=['POST'])
def insert_metadata():
    try:
        request_data = request.get_json()
        if not request_data:
            raise APIError("Tenant config is required", 400)

        tenant_config = TenantConfig.from_dict(request_data)
        metadata = metadata_loader_service.load_all_metadata()

        for tenant in tenant_config.tenants:
            metadata_service.process_metadata(tenant, metadata)
        return jsonify({'message': 'Metada<PERSON> inserted successfully'}), 201

    except ValueError as e:
        raise APIError(str(e), 400)

