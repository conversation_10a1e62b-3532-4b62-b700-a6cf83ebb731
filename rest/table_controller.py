from flask import request, jsonify

import service.table_service as table_service
import service.column_service as column_service
from interceptor.thread_context import ThreadLocalContext

from models.meta_data import *
from utils.global_exception_handler import APIError
from . import api_v1
from flasgger import swag_from

table_schema = TrinoTableMetaDataSchema()
tables_schema = TrinoTableMetaDataSchema(many=True)


@api_v1.route('/databases/<db_name>/tables', methods=['GET'])
@swag_from({
    'tags': ['Tables'],
    'parameters': [
        {
            'name': 'db_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the database whose tables are to be retrieved'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the database'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns a list of tables within the specified database',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/TableMetaData'
                }
            }
        },
        '404': {
            'description': 'Database not found'
        }
    }
})
def get_tables_by_database(db_name):
    """
    Retrieve all tables within a specific database.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    tables = table_service.find_by_db_name(db_name, tenant_name)
    return tables_schema.jsonify(tables)

@api_v1.route('/tables', methods=['POST'])
@swag_from({
    'tags': ['Tables'],
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the user.'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/TableMetaData'
            }
        }
    ],
    'responses': {
        '201': {
            'description': 'Successfully created a new table',
            'schema': {
                '$ref': '#/definitions/TableMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        }
    }
})
def create_table():
    """
    Create a new table in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Table data is required.", 400)

    try:
        table = table_schema.load(data, session=db.session)
    except ValueError as e:
        raise APIError(str(e), 400)

    table = table_service.create(table)
    return table_schema.jsonify(table), 201

@api_v1.route('/tables/<table_name>', methods=['GET'])
@swag_from({
    'tags': ['Tables'],
    'parameters': [
        {
            'name': 'table_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the table to retrieve'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the table'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns details of the specified table',
            'schema': {
                '$ref': '#/definitions/TableMetaData'
            }
        },
        '404': {
            'description': 'Table not found'
        }
    }
})
def get_table(table_name):
    """
    Retrieve details of a specific table using its unique name.
    """
    tenant_name = request.headers['Tenant-Name']
    table_meta = table_service.find_by_table_name(table_name, tenant_name)
    if table_meta is None:
        raise APIError("TableMetaData not found", 404)
    return table_schema.jsonify(table_meta)

@api_v1.route('/tables', methods=['GET'])
@swag_from({
    'tags': ['Tables'],
    'parameters': [
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name to filter tables'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns a list of all tables in the system',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/TableMetaData'
                }
            }
        }
    }
})
def get_tables():
    """
    Retrieve a list of all tables in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    tables = table_service.find_all(tenant_name)
    return tables_schema.jsonify(tables)

@api_v1.route('/tables/<table_name>', methods=['PUT'])
@swag_from({
    'tags': ['Tables'],
    'parameters': [
        {
            'name': 'table_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the table to be updated'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/TableMetaData'
            }
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the table'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully updated the table',
            'schema': {
                '$ref': '#/definitions/TableMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        },
        '404': {
            'description': 'Table not found'
        }
    }
})
def update_table(table_name):
    """
    Update an existing table's metadata.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Table data is required.", 400)

    existing = table_service.find_by_table_name(table_name, tenant_name)
    if existing is None:
        raise APIError("TableMetaData not found", 404)

    try:
        for key, value in data.items():
            setattr(existing, key, value)
    except ValueError as e:
        raise APIError(str(e), 400)

    table = table_service.update(existing)
    return table_schema.jsonify(table)

@api_v1.route('/tables/<table_name>', methods=['DELETE'])
@swag_from({
    'tags': ['Tables'],
    'parameters': [
        {
            'name': 'table_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the table to be deleted'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the table'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully deleted the table'
        },
        '404': {
            'description': 'Table not found'
        }
    }
})
def delete_table(table_name):
    """
    Delete a table using its unique name.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    existing = table_service.find_by_table_name(table_name, tenant_name)
    if existing is None:
        raise APIError("TableMetaData not found", 404)

    columns = column_service.find_by_table_name(table_name, tenant_name)

    for column in columns:
        column_service.delete(column)

    table_service.delete(existing)
    return jsonify({"message": "TableMetaData deleted successfully"})
