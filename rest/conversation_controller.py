import threading
import time
from datetime import datetime
from queue import Queue, Empty

from flasgger import swag_from
from flask import request, jsonify, Response
from langchain_core.messages import HumanMessage

import service.conversation_service as conversation_service
from config.app_config import Config
from dal.conversation_dal import get_chat_history
from interceptor.distributed_lock_interceptor import acquire_lock, release_lock
from interceptor.thread_context import ThreadLocalContext
from models.conversation_meta_data import ConversationSchema
from models.llm_response import ResponseSchema
from utils.LogUtils import LOG
from utils.PaginationUtils import get_pageable_from_request
from utils.global_exception_handler import APIError
from . import api_v1

response_schema = ResponseSchema()
conversation_schema = ConversationSchema()
conversations_schema = ConversationSchema(many=True)


@api_v1.route('/conversations', methods=['POST'])
@swag_from({
    'responses': {
        '201': {
            'description': 'New conversation started',
            'schema': {
                '$ref': '#/definitions/Conversation'
            }
        }
    },
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user initiating the conversation'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The name of the tenant'
        },
        {
            'name': 'module_identifier',
            'in': 'query',
            'type': 'string',
            'required': True,
            'description': 'Identifier for the module (e.g., event-timeline).'
        },
        {
            'name': 'entity_type',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'The type of the entity (e.g., vessel).'
        },
        {
            'name': 'entity_id',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'The ID of the entity (e.g., IMO 8952546).'
        }
    ],
    'description': 'Start a new conversation for a user',
    'tags': ['Conversations']
})
def start_conversation():
    """
    Start a new conversation for a user
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()

        module_identifier = request.args.get('module_identifier')
        entity_type = request.args.get('entity_type')
        entity_id = request.args.get('entity_id')
        entity_name = request.args.get('entity_name')

        if not module_identifier:
            raise APIError("Invalid input. module_identifier is required.", 400)

        conversation = conversation_service.create_new_conversation(user_id, tenant_name, module_identifier, entity_type, entity_id, entity_name)
        return conversation_schema.jsonify(conversation), 201
    except ValueError as e:
        raise APIError(str(e), 400)


@api_v1.route('/conversations/<conversation_id>/messages', methods=['POST'])
@swag_from({
    'responses': {
        '200': {
            'description': 'Response from the assistant for the user question',
            'schema': {
                'type': 'object',
                'properties': {
                    'file': {
                        'type': 'object',
                        'description': 'File object or null if no file is returned'
                    },
                    'text': {
                        'type': 'string',
                        'description': 'The assistant\'s textual response'
                    }
                }
            }
        }
    },
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The user ID of the person sending the question'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name'
        },
        {
            'name': 'conversation_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the conversation'
        },
        {
            'name': 'question',
            'in': 'body',
            'schema': {
                'type': 'object',
                'properties': {
                    'question': {
                        'type': 'string',
                        'description': 'The question text to be sent to the assistant',
                    }
                }
            },
            'required': True,
            'description': 'The question asked by the user'
        }
    ],
    'description': 'Send a question in a conversation and receive a response from the assistant. The response will '
                   'contain either a textual answer or a file (or both).',
    'tags': ['Conversations']
})
def answer_question(conversation_id):
    """
    Send a question in a conversation and receive a response from the assistant.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        data = request.get_json()

        if not data or 'question' not in data:
            raise APIError("Invalid input. Question data is required.", 400)
        if not conversation_id:
            raise APIError("Invalid input. conversation_id is required.", 400)

        # Prepare lock key
        lock_key = f"conversation/{conversation_id}"

        # Try acquiring the lock; fail immediately if already locked
        connection = acquire_lock(lock_key)

        try:
            convo_start_time = time.time()
            chat_message_history = get_chat_history(conversation_id)
            chat_message_history.add_user_message(HumanMessage(content=data['question'],additional_kwargs={"timestamp": datetime.now().strftime("%H:%M:%S")}))
            response = conversation_service.answer_question(user_id, conversation_id, tenant_name, data['question'])
            output = {"text":response}
            convo_end_time = time.time()
            LOG.success(f"Time Taken for Response : {convo_end_time-convo_start_time}")
            return output
        finally:
            release_lock(connection, lock_key)  # Ensure lock is released
    except ValueError as e:
        raise APIError(str(e), 400)


@api_v1.route('/conversations/<conversation_id>/messages/stream', methods=['POST'])
@swag_from({
    'responses': {
        '200': {
            'description': 'Response from the assistant for the user question',
            'schema': {
                'type': 'object',
                'properties': {
                    'file': {
                        'type': 'object',
                        'description': 'File object or null if no file is returned'
                    },
                    'text': {
                        'type': 'string',
                        'description': 'The assistant\'s textual response'
                    }
                }
            }
        }
    },
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The user ID of the person sending the question'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name'
        },
        {
            'name': 'conversation_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the conversation'
        },
        {
            'name': 'question',
            'in': 'body',
            'schema': {
                'type': 'object',
                'properties': {
                    'question': {
                        'type': 'string',
                        'description': 'The question text to be sent to the assistant',
                    }
                }
            },
            'required': True,
            'description': 'The question asked by the user'
        }
    ],
    'description': 'Send a question in a conversation and streams a response from the assistant. The response will '
                   'contain either a textual answer or a file (or both).',
    'tags': ['Conversations']
})
def answer_question_stream(conversation_id):
    """
    Send a question in a conversation and streams a response from the assistant.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        data = request.get_json()
        question = data['question']
        q = Queue()

        # Prepare lock key
        lock_key = f"conversation/{conversation_id}"
        connection = acquire_lock(lock_key)  # Manually acquire lock

        def process():
            """Function executed inside the thread to process the request."""
            try:
                conversation_service.answer_question_stream(user_id, conversation_id, tenant_name, question, q)
            finally:
                release_lock(connection, lock_key)  # Ensure lock is released after processing

        thread = threading.Thread(target=process, daemon=True)
        thread.start()
        LOG.info(f"New thread created = {thread}")

        def generate():
            while True:
                try:
                    # Wait for message with timeout
                    message = q.get(timeout=1.0)  # 1 second timeout
                    if message is None:
                        break
                    yield message
                except Empty:
                    # Check if thread is still alive
                    if thread.is_alive():
                        continue
                    else:
                        LOG.warn("Thread no longer alive, ending stream")
                        break

        response = Response(generate(), mimetype='text/event-stream')
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['Connection'] = 'keep-alive'
        return response

    except ValueError as e:
        raise APIError(str(e), 400)


@api_v1.route('/conversations', methods=['GET'])
@swag_from({
    'responses': {
        '200': {
            'description': 'List of all conversations for the user',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/Conversation'
                }
            }
        }
    },
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user whose conversations are being retrieved'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The name of the tenant'
        },
        {
            'name': 'pageNumber',
            'in': 'query',
            'type': 'integer',
            'required': False,
            'description': 'Page number for pagination. Defaults to 1.'
        },
        {
            'name': 'pageSize',
            'in': 'query',
            'type': 'integer',
            'required': False,
            'description': 'Number of items per page for pagination. Defaults to 10.'
        },
        {
            'name': 'sortBy',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'Field to sort by. Defaults to "timestamp".'
        },
        {
            'name': 'sortOrder',
            'in': 'query',
            'type': 'string',
            'enum': ['asc', 'desc'],
            'required': False,
            'description': 'Sort order. Defaults to "desc".'
        }
    ],
    'description': 'GET all the conversations of the User.',
    'tags': ['Conversations']
})
def get_conversations():
    """
    GET all the conversations of the User.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        pageable = get_pageable_from_request(request.args)

        conversations, total = conversation_service.find_by_user_and_tenant(user_id, tenant_name, pageable)
        response = {
            "data": conversations_schema.dump(conversations),
            "total": total
        }
        return jsonify(response)
    except ValueError as e:
        raise APIError(str(e), 400)


@api_v1.route('/conversations/<conversation_id>/messages', methods=['GET'])
@swag_from({
    'responses': {
        '200': {
            'description': 'Messages retrieved for the specified conversation',
            'schema': {
                'type': 'array',
                'items': {
                    'type': 'object',
                    'properties': {
                        'role': {
                            'type': 'string',
                            'description': 'The role of the message sender (e.g., "assistant" or "user")',
                        },
                        'text': {
                            'type': 'string',
                            'description': 'The text of the message',
                        },
                        'timestamp': {
                            'type': 'integer',
                            'description': 'The timestamp of the message',
                        }
                    }
                }
            }
        }
    },
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user retrieving the messages'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The name of the tenant'
        },
        {
            'name': 'conversation_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the conversation'
        },
        {
            'name': 'pageNumber',
            'in': 'query',
            'type': 'integer',
            'required': False,
            'description': 'Page number for pagination. Defaults to 1.'
        },
        {
            'name': 'pageSize',
            'in': 'query',
            'type': 'integer',
            'required': False,
            'description': 'Number of items per page for pagination. Defaults to 10.'
        },
        {
            'name': 'sortBy',
            'in': 'query',
            'type': 'string',
            'required': False,
            'description': 'Field to sort by. Defaults to "timestamp".'
        },
        {
            'name': 'sortOrder',
            'in': 'query',
            'type': 'string',
            'enum': ['asc', 'desc'],
            'required': False,
            'description': 'Sort order. Defaults to "desc".'
        }
    ],
    'description': 'Get the messages in a conversation.',
    'tags': ['Conversations']
})
def get_messages(conversation_id):
    """
    Get the messages in a conversation.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        pageable = get_pageable_from_request(request.args)

        if not conversation_id:
            raise APIError("Invalid input. conversation_id is required.", 400)

        messages, total = conversation_service.get_messages_from_mongo(conversation_id, pageable)
        response = {
            "data": messages,
            "total": total
        }

        return jsonify(response)
    except ValueError as e:
        print(e)
        raise APIError(str(e), 400)

# def get_messages(conversation_id):
#     """
#     Get the messages in a conversation.
#     """
#     try:
#         user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
#         pageable = get_pageable_from_request(request.args)
#
#         if not conversation_id:
#             raise APIError("Invalid input. conversation_id is required.", 400)
#
#         messages, total = conversation_service.get_messages(user_id, tenant_name, conversation_id, pageable)
#         response = {
#             "data": messages,
#             "total": total
#         }
#         return jsonify(response)
#     except ValueError as e:
#         raise APIError(str(e), 400)

@api_v1.route('/conversations', methods=['DELETE'])
@swag_from({
    'summary': 'Delete Conversations',
    'description': 'Delete all conversations for the specified user and tenant, except those with IDs in the excluded list.',
    'responses': {
        '200': {
            'description': 'Conversations deleted successfully except the excluded ones',
            'schema': {
                'type': 'object',
                'properties': {
                    'message': {
                        'type': 'string',
                        'description': 'Success message indicating conversations were cleared',
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid input or user authentication failure',
            'schema': {
                'type': 'object',
                'properties': {
                    'message': {
                        'type': 'string',
                        'description': 'Error message indicating the cause of failure'
                    }
                }
            }
        }
    },
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user whose conversations will be cleared'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The name of the tenant'
        },
        {
            'name': 'excluded_ids',
            'in': 'query',
            'type': 'array',
            'items': {
                'type': 'string'
            },
            'required': False,
            'description': 'List of conversation IDs to be excluded from deletion'
        }
    ],
    'tags': ['Conversations']
})
def clear_conversations():
    """
    Delete all conversations for the specified user and tenant, except the current conversation.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        excluded_ids = request.args.get('excluded_ids')
        if excluded_ids is None:
            excluded_ids = ['']
        else:
            excluded_ids = excluded_ids.split(',')

        conversation_service.clear_conversation(user_id, tenant_name, excluded_ids)
        return jsonify({"message": "Cleared previous conversation successfully"})
    except ValueError as e:
        raise APIError(str(e), 400)


@api_v1.route('/conversations/<conversation_id>', methods=['DELETE'])
@swag_from({
    'summary': 'Delete Specific Conversation',
    'description': 'Delete a specific conversation by its ID for the authenticated user.',
    'responses': {
        '200': {
            'description': 'Conversation deleted successfully',
            'schema': {
                'type': 'object',
                'properties': {
                    'message': {
                        'type': 'string',
                        'description': 'Success message indicating the conversation was deleted',
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid input or failure due to invalid conversation ID',
            'schema': {
                'type': 'object',
                'properties': {
                    'message': {
                        'type': 'string',
                        'description': 'Error message indicating the cause of failure'
                    }
                }
            }
        },
        '404': {
            'description': 'Conversation not found',
            'schema': {
                'type': 'object',
                'properties': {
                    'message': {
                        'type': 'string',
                        'description': 'Error message indicating that the conversation was not found'
                    }
                }
            }
        }
    },
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user requesting the deletion'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The name of the tenant'
        },
        {
            'name': 'conversation_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the conversation to be deleted'
        }
    ],
    'tags': ['Conversations']
})
def delete_conversation(conversation_id):
    """
    Delete a specific conversation by its ID.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()

        if not conversation_id:
            raise APIError("Invalid input. conversation_id is required.", 400)

        conversation_service.delete_conversation(user_id, tenant_name, conversation_id)
        return jsonify({"message": "Conversation deleted successfully"})
    except ValueError as e:
        raise APIError(str(e), 400)


@api_v1.route('/conversations/recent', methods=['GET'])
@swag_from({
    'tags': ['Conversations'],
    'summary': 'Get the most recent conversation object',
    'description': 'Fetches the most recent conversation for the user, including its metadata and details.',
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user whose recent conversation is being fetched.'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully retrieved the recent conversation.',
            'schema': {
                'type': 'object',
                'properties': {
                    'conversation': {
                        'type': 'object',
                        'description': 'The details of the most recent conversation.',
                        'example': {
                            'id': '123e4567-e89b-12d3-a456-426614174000',
                            'user_id': 'user123',
                            'openai_thread_id': 'thread123',
                            'tenant_name': 'example_tenant',
                            'first_message': 'Hello, how can I help?',
                            'createdAt': '2024-11-27T12:34:56.789000',
                            'lastUpdatedAt': '2024-12-01T15:45:23.678000'
                        }
                    }
                }
            }
        },
        '400': {
            'description': 'Bad Request - Missing required headers.',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {
                        'type': 'string',
                        'description': 'Error message describing the missing or invalid input.',
                        'example': 'User-Id and Tenant-Name headers are required.'
                    }
                }
            }
        },
        '404': {
            'description': 'No recent conversation found.',
            'schema': {
                'type': 'object',
                'properties': {
                    'message': {
                        'type': 'string',
                        'description': 'Message indicating no conversation was found.',
                        'example': 'No recent conversations found.'
                    }
                }
            }
        }
    }
})
def get_recent_chat():
    """
    Get the most recent conversation for a user and tenant.
    """
    try:
        user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
        recent_conversation = conversation_service.get_recent_conversation(user_id, tenant_name)
        if not recent_conversation:
            raise APIError("No recent conversations found.", 404)

        return conversation_schema.dump(recent_conversation), 200
    except ValueError as e:
        raise APIError(str(e), 404)
