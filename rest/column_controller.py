from flask import request, jsonify
import service.column_service as column_service
from interceptor.thread_context import ThreadLocalContext
from models.meta_data import *
from utils.global_exception_handler import APIError
from . import api_v1
from flasgger import swag_from

column_schema = TrinoColumnMetaDataSchema()
columns_schema = TrinoColumnMetaDataSchema(many=True)


@api_v1.route('/tables/<table_name>/columns', methods=['GET'])
@swag_from({
    'tags': ['Columns'],
    'parameters': [
        {
            'name': 'table_name',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The name of the table whose columns are being retrieved'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the table'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns a list of columns for the specified table',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/ColumnMetaData'
                }
            }
        },
        '404': {
            'description': 'Table not found or has no columns'
        }
    }
})
def get_columns_by_table(table_name):
    """
    Retrieve a list of columns associated with the specified table.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    columns = column_service.find_by_table_name(table_name, tenant_name)
    return columns_schema.jsonify(columns)


@api_v1.route('/columns', methods=['POST'])
@swag_from({
    'tags': ['Columns'],
    'parameters': [
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the user.'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/ColumnMetaData'
            }
        }
    ],
    'responses': {
        '201': {
            'description': 'Successfully created a new column',
            'schema': {
                '$ref': '#/definitions/ColumnMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        }
    }
})
def create_column():
    """
    Create a new column in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Column data is required.", 400)

    try:
        column = column_schema.load(data)
    except ValueError as e:
        raise APIError(str(e), 400)

    column = column_service.create(column)
    return column_schema.jsonify(column), 201


@api_v1.route('/columns/<column_id>', methods=['GET'])
@swag_from({
    'tags': ['Columns'],
    'parameters': [
        {
            'name': 'column_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the column to retrieve'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the column'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns details of the specified column',
            'schema': {
                '$ref': '#/definitions/ColumnMetaData'
            }
        },
        '404': {
            'description': 'Column not found'
        }
    }
})
def get_column(column_id):
    """
    Retrieve details of a specific column using its unique ID.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    column = column_service.find_by_id(column_id, tenant_name)
    if column is None:
        raise APIError("ColumnMetaData not found", 404)
    return column_schema.jsonify(column)


@api_v1.route('/columns', methods=['GET'])
@swag_from({
    'tags': ['Columns'],
    'parameters': [
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name to filter columns'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Returns a list of all columns in the system',
            'schema': {
                'type': 'array',
                'items': {
                    '$ref': '#/definitions/ColumnMetaData'
                }
            }
        }
    }
})
def get_columns():
    """
    Retrieve a list of all columns in the system.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    columns = column_service.find_all(tenant_name=tenant_name)
    return columns_schema.jsonify(columns)


@api_v1.route('/columns/<column_id>', methods=['PUT'])
@swag_from({
    'tags': ['Columns'],
    'parameters': [
        {
            'name': 'column_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the column to be updated'
        },
        {
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                '$ref': '#/definitions/ColumnMetaData'
            }
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the column'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully updated the column',
            'schema': {
                '$ref': '#/definitions/ColumnMetaData'
            }
        },
        '400': {
            'description': 'Invalid input provided'
        },
        '404': {
            'description': 'Column not found'
        }
    }
})
def update_column(column_id):
    """
    Update an existing column's metadata.
    """
    data = request.get_json()
    if not data:
        raise APIError("Invalid input. Column data is required.", 400)

    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    existing = column_service.find_by_id(column_id, tenant_name)
    if existing is None:
        raise APIError("ColumnMetaData not found", 404)

    try:
        for key, value in data.items():
            setattr(existing, key, value)
    except ValueError as e:
        raise APIError(str(e), 400)

    column = column_service.update(existing)
    return column_schema.jsonify(column)


@api_v1.route('/columns/<column_id>', methods=['DELETE'])
@swag_from({
    'tags': ['Columns'],
    'parameters': [
        {
            'name': 'column_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'The ID of the column to be deleted'
        },
        {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The tenant name associated with the column'
        },
        {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user.'
        }
    ],
    'responses': {
        '200': {
            'description': 'Successfully deleted the column'
        },
        '404': {
            'description': 'Column not found'
        }
    }
})
def delete_column(column_id):
    """
    Delete a column using its unique ID.
    """
    user_id, tenant_name = ThreadLocalContext.get_user_and_tenant()
    existing = column_service.find_by_id(column_id, tenant_name)
    if existing is None:
        raise APIError("ColumnMetaData not found", 404)

    column_service.delete(existing)
    return jsonify({"message": "ColumnMetaData deleted successfully"})
