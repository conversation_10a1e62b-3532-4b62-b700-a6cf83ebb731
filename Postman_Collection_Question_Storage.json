{"info": {"name": "Question Query History API Collection", "description": "Complete API collection to store questions in question_query_history table", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3005"}, {"key": "conversation_id", "value": ""}], "item": [{"name": "1. Create New Conversation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('conversation_id', response.id);", "    console.log('Conversation ID saved:', response.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "User-Id", "value": "test-user-123"}, {"key": "Tenant-Name", "value": "stage"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/conversations?module_identifier=event-timeline", "host": ["{{base_url}}"], "path": ["api", "v1", "conversations"], "query": [{"key": "module_identifier", "value": "event-timeline"}]}, "body": {"mode": "raw", "raw": "{}"}}}, {"name": "2. Ask V<PERSON>el Count Question", "request": {"method": "POST", "header": [{"key": "User-Id", "value": "test-user-123"}, {"key": "Tenant-Name", "value": "stage"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/conversations/{{conversation_id}}/messages", "host": ["{{base_url}}"], "path": ["api", "v1", "conversations", "{{conversation_id}}", "messages"]}, "body": {"mode": "raw", "raw": "{\n    \"question\": \"How many vessels are in the fleet?\"\n}"}}}, {"name": "3. <PERSON>el Names and IMO", "request": {"method": "POST", "header": [{"key": "User-Id", "value": "test-user-123"}, {"key": "Tenant-Name", "value": "stage"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/conversations/{{conversation_id}}/messages", "host": ["{{base_url}}"], "path": ["api", "v1", "conversations", "{{conversation_id}}", "messages"]}, "body": {"mode": "raw", "raw": "{\n    \"question\": \"Show me vessel names and IMO numbers\"\n}"}}}, {"name": "4. Ask Total Capacity", "request": {"method": "POST", "header": [{"key": "User-Id", "value": "test-user-123"}, {"key": "Tenant-Name", "value": "stage"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/conversations/{{conversation_id}}/messages", "host": ["{{base_url}}"], "path": ["api", "v1", "conversations", "{{conversation_id}}", "messages"]}, "body": {"mode": "raw", "raw": "{\n    \"question\": \"What is the total capacity of vessels?\"\n}"}}}, {"name": "5. Ask Math Question (Won't store SQL)", "request": {"method": "POST", "header": [{"key": "User-Id", "value": "test-user-123"}, {"key": "Tenant-Name", "value": "stage"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/conversations/{{conversation_id}}/messages", "host": ["{{base_url}}"], "path": ["api", "v1", "conversations", "{{conversation_id}}", "messages"]}, "body": {"mode": "raw", "raw": "{\n    \"question\": \"What is 5+9?\"\n}"}}}, {"name": "6. Get All Conversations", "request": {"method": "GET", "header": [{"key": "User-Id", "value": "test-user-123"}, {"key": "Tenant-Name", "value": "stage"}], "url": {"raw": "{{base_url}}/api/v1/conversations", "host": ["{{base_url}}"], "path": ["api", "v1", "conversations"]}}}]}