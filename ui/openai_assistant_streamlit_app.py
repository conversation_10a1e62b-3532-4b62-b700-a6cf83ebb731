from io import BytesIO

import streamlit as st
from PIL import Image

import service.llm.openai_assistant_ai_service as aiSvc
from utils.JsonUtils import *
from flask_app import app

st.set_page_config(
    page_title="Platform AI - OPEN AI ASSISTANT",
    layout="wide"
)

st.title("Platform AI - OPEN AI ASSISTANT")

if "messages" not in st.session_state:
    st.session_state.messages = []


def display_message(role, response):
    if role == "user":
        with st.chat_message(role):
            st.markdown(response)
    elif role == "assistant":
        if response.text:
            st.markdown(response.text)
        if response.file:
            image_content = response.file.content
            image = Image.open(BytesIO(image_content))
            st.image(image)
        st.success(f"Request processed in {response.total_processing_time} seconds")
        #st.info(f"Total time needed by LLM = {response.llm_processing_time} seconds")


# Display previous messages
for message in st.session_state.messages:
    display_message(message["role"], message["content"])

user_info = {
    'DEV USER': {'tenant': "dev"},
    'STAGE USER': {'tenant': "stage"}
}

selected_user = st.selectbox("Select a user:", options=list(user_info.keys()))

if selected_user:
    selected_details = user_info[selected_user]
    selected_tenant = selected_details['tenant']

    if question := st.chat_input("Ask me about information in the database..."):
        LOG.info(f"The question asked by User = {question}")
        st.session_state.messages.append({"role": "user", "content": question})
        display_message("user", question)

        with st.chat_message("assistant"):
            message_placeholder = st.empty()
            with app.app_context():
                response = aiSvc.answerQuestionWithSystemThread(f"[TENANT = {selected_tenant}]" + question)
                display_message("assistant", response)
                st.session_state.messages.append(
                    {
                        "role": "assistant",
                        "content": response
                    }
                )
