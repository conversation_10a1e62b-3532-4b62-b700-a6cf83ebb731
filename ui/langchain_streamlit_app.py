import streamlit as st

import service.llm.langchain_ai_service as aiSvc
from models.llm_response import ResponseSchema
from utils.JsonUtils import *
from flask_app import app

schema = ResponseSchema()

st.set_page_config(
    page_title="PLATFORM AI - Langchain",
    layout="wide"
)

st.title("PLATFORM AI - Langchain")

if "messages" not in st.session_state:
    st.session_state.messages = []


def display_message(role, content, backend_details=None):
    with st.chat_message(role):
        if role == 'user':
            st.markdown(content)
        else:
            format_response(
                content.text,
                content.file.path if content.file else None,
                content.file.caption if content.file else None
            )

        if backend_details:
            with st.expander("Function calls, parameters, and responses"):
                display_func_responses(backend_details)


def format_response(text, file_path=None, file_caption=None):
    st.markdown(text)
    if file_path:
        st.image(file_path, caption=file_caption)
    else:
        st.warning("File path is missing or None.")


def display_func_responses(func_requests_responses):
    for call in func_requests_responses:
        tool_call_id, function_name, args, json_response = call

        st.write(f"**Tool Call ID:** {tool_call_id}")
        st.write(f"**Function Name:** {function_name}")

        st.write("**Arguments:**")
        st.json(args)

        st.write("**Response:**")
        st.json(json_response)

        st.markdown("---")


# Display previous messages
for message in st.session_state.messages:
    display_message(message["role"], message["content"], message.get("backend_details"))


if question := st.chat_input("Ask me about information in the database..."):
    LOG.info(f"The question asked by User = {question}")
    st.session_state.messages.append({"role": "user", "content": question})
    display_message("user", question)

    with st.chat_message("assistant"):
        message_placeholder = st.empty()

        with app.app_context():
            llm_response, func_requests_responses = aiSvc.answerQuestion(question)

            with message_placeholder.container():
                resp_json = format_llm_response_to_json(llm_response.content)
                llm_response_model = schema.load(resp_json)

                LOG.info(f"The response model - {llm_response_model}")

                format_response(
                    llm_response_model.text,
                    llm_response_model.file.path if llm_response_model.file else None,
                    llm_response_model.file.caption if llm_response_model.file else None
                )

                with st.expander("Function calls, parameters, and responses:"):
                    display_func_responses(func_requests_responses)

            st.session_state.messages.append(
                {
                    "role": "assistant",
                    "content": llm_response_model,
                    "backend_details": func_requests_responses
                }
            )
