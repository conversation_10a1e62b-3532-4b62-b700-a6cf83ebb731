import json
from pymongo import MongoClient
from pymongo.errors import PyMongoError

from utils.JsonUtils import json_serializable
from utils.LogUtils import LOG

'''
We have instructed AI to use aggregation query only
'''
def execute_query(datasource_url, database_name, table_name, query):
    try:
        # TODO - Hack as AI is not providing this. Please remove this
        datasource_url = "mongodb://localhost:27017/"
        query_dict = json.loads(query)
        client = MongoClient(datasource_url)
        db = client[database_name]
        collection = db[table_name]
        results = collection.aggregate(query_dict)
        serializable_results = [json_serializable(result) for result in results]
        return serializable_results

    except (PyMongoError, json.JSONDecodeError, Exception) as e:
        LOG.error(f"An error occurred: {str(e)}")
        return {"result": "query failed. Try a different query"}

