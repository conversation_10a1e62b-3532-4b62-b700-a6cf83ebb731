"""Trino Query Executor Module

This module provides functionality for executing SQL queries against a Trino database.
It includes tools for executing single queries and parallel queries, with built-in
security measures to prevent unsafe queries from being executed.

The module provides the following main components:
- TrinoConnectionPool: A connection pool for managing Trino database connections
- parallel_sql_query_executor: A tool for executing multiple SQL queries in parallel
- sql_query_executor: A tool for executing a single SQL query
- sanitize_query: A function for sanitizing and validating SQL queries
- replace_vessel_ids_with_count: A utility for processing vessel_ids in query results

These components work together to provide a secure and efficient way to execute
SQL queries against a Trino database and process the results.
"""

import ast
import json
import re
import time
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from typing import Union, Dict, Any
from datetime import datetime
import pytz
import pandas as pd
from langchain.agents import tool
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
from trino.dbapi import connect
import service.column_service as column_svc

from config.app_config import Config
from dal.question_query_history_dal import create_question_query_entry, QuestionQueryManager
from utils.LogUtils import LOG, ENV

manager = QuestionQueryManager()


class TrinoConnectionPool:
    """
    A connection pool for Trino database connections.

    This class manages a pool of Trino database connections using SQLAlchemy's
    QueuePool, allowing for efficient reuse of connections.
    """
    def __init__(self, host, port, user, pool_size=5, max_overflow=10, timeout=30):
        """
        Initialize a new Trino connection pool.

        Args:
            host (str): The hostname or IP address of the Trino server.
            port (int): The port number of the Trino server.
            user (str): The username to connect with.
            pool_size (int, optional): The number of connections to keep open. Defaults to 5.
            max_overflow (int, optional): The maximum number of connections to create beyond pool_size. Defaults to 10.
            timeout (int, optional): The number of seconds to wait before timing out. Defaults to 30.
        """
        self.connection_params = {
            "host": host,
            "port": port,
            "user": user,
        }
        self.engine = create_engine(
            f"trino://{host}:{port}",
            creator=self._create_connection,
            poolclass=QueuePool,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=timeout
        )

    def _create_connection(self):
        """
        Create a new connection to the Trino database.

        Returns:
            Connection: A new connection to the Trino database.
        """
        return connect(**self.connection_params)

    @contextmanager
    def get_connection(self):
        """
        Get a connection from the pool.

        This is a context manager that yields a connection from the pool and
        ensures it is closed when the context exits.

        Yields:
            Connection: A connection from the pool.
        """
        conn = self.engine.raw_connection()
        try:
            yield conn
        finally:
            conn.close()


pool = TrinoConnectionPool(
    host=Config.TRINO_HOST,
    port=Config.TRINO_PORT,
    user="trino"
)


@tool
def parallel_sql_query_executor(queries: dict, question: str) -> Dict[str, Any]:
    """
    Executes multiple SQL queries in parallel and returns all results.
    Args:
        queries: A dictionary of SQL query strings to execute in parallel.
        question: Question asked by the user.
    Returns:
        A dictionary mapping each query identifier to its parsed result.
    """
    LOG.trace(queries)
    parsed_queries = list(dict.fromkeys(queries.values()))
    question = question.split('=>')[1].strip() if '=>' in question else question
    LOG.info(f"Parallel Query Processing: {len(parsed_queries)}")

    results = {
        "query_results": {},
        "parsed_results": {},
        "original_queries": {}
    }

    def parse_json_result(result):
        """
        Parses a string result into JSON, if possible.

        Args:
            result: The result to parse, which may be a string containing JSON or another type.

        Returns:
            The parsed JSON object if successful, or the original result if parsing fails or if
            the input is not a string.
        """
        if isinstance(result, str):
            try:
                return json.loads(result)
            except (json.JSONDecodeError, TypeError):
                return result
        return result

    def execute_single_query(query_str: str, user_question: str) -> Dict[str, Any]:
        """
        Execute a single SQL query and parse its result.

        This function is designed to be used with ThreadPoolExecutor for parallel query execution.

        Args:
            query_str (str): The SQL query string to execute.
            user_question (str): The natural language question that prompted this query.

        Returns:
            Dict[str, Any]: A dictionary containing the execution status, raw result, and parsed result.
            If successful, the dictionary will have the following structure:
            {
                "success": True,
                "raw_result": <raw result from sql_query_executor>,
                "parsed_result": <parsed JSON result>
            }
            If an error occurs, the dictionary will have the following structure:
            {
                "success": False,
                "error": <error message>
            }
        """
        try:
            result = sql_query_executor.invoke({"query": query_str, "question": user_question})
            parsed_result = parse_json_result(result)

            return {
                "success": True,
                "raw_result": result,
                "parsed_result": parsed_result
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    with ThreadPoolExecutor(max_workers=Config.MAX_WORKERS or 5) as executor:
        futures = {}
        for i, query in enumerate(parsed_queries):
            query_num = f"Query {i + 1}"
            futures[executor.submit(execute_single_query, query, question)] = query_num
            results["original_queries"][query_num] = query

        for future in futures:
            query_num = futures[future]
            try:
                query_result = future.result(timeout=Config.TIMEOUT)

                if query_result.get("success"):
                    results["query_results"][query_num] = query_result["raw_result"]
                    results["parsed_results"][query_num] = query_result["parsed_result"]
                else:
                    results["query_results"][query_num] = {"error": query_result.get("error", "Unknown error")}
            except TimeoutError:
                timeout_msg = f"Query execution timed out after {Config.TIMEOUT} seconds."
                results["query_results"][query_num] = {"error": timeout_msg}
            except Exception as e:
                results["query_results"][query_num] = {"error": f"Execution failed: {str(e)}"}

    return results['parsed_results']


@tool
def sql_query_executor(query: str, question: str) -> Union[str, dict]:
    """
    Executes a Trino SQL query and returns results as JSON or error string.

    This function sanitizes the input query, executes it against the Trino database,
    and returns the results as a JSON string. It also logs the query and results,
    and stores the query in the database for future reference.

    Args:
        query (str): SQL query string to execute.
        question (str): Natural language question used for logging/context.

    Returns:
        Union[str, dict]: JSON result string if successful, or an error dictionary if the query fails.
        The error dictionary has the format: {"result": "error message"}.
    """
    LOG.info(f"Executing Trino query on {Config.TRINO_HOST}:{Config.TRINO_PORT}")
    LOG.info(f"Question Received: {question}")

    try:
        query = query.replace('enhanced_timeline','\"enhanced-timeline\"')

        if ENV.lower() in ['dev', 'uat']:
            query = re.sub(r'\bevents_raw\b(?!_revised)', 'events_raw_revised', query)

        query = sanitize_query(query)

        if query is None:
            return {"result": "Create a valid query and maintain the tool input format."}

        with pool.get_connection() as conn:
            cursor = conn.cursor()
            start_time = time.time()

            sanitized_query = query.strip().rstrip(';')
            cursor.execute(sanitized_query)
            results = cursor.fetchall()

            end_time = time.time()
            LOG.info(f"Time Taken to Execute Query: {end_time - start_time:.2f} seconds")

            df = pd.DataFrame(results, columns=[desc[0] for desc in cursor.description])
            if df.empty:
                return {"result": "Empty, Try some other query to get data."}

            json_result = df.to_json(orient='records', date_format='iso', indent=2)
            json_result = replace_vessel_ids_with_count(json_result)

            if question.strip():
                manager.upsert_question_with_queries(question, [sanitized_query])

            LOG.info(f"Query Result: {json_result}")
            return json_result

    except Exception as e:
        LOG.warning(f"An error occurred: {e}")
        if question:
            manager.upsert_question_with_queries(question, [sanitized_query])
        return {"result": f"Query failed. Try a different query."}
    finally:
        if 'conn' in locals():
            conn.close()


def sanitize_query(query, self_call: bool = False):
    """
    Sanitize the input query and ensure it is read-only.

    This function performs several security checks on the input query to ensure it is safe to execute:
    1. Removes code block formatting and SQL comments
    2. Blocks any data modification or definition language statements (DDL, DML, DCL)
    3. Blocks system commands and references to system tables
    4. Ensures the query starts with SELECT, WITH, or SHOW

    Args:
        query (str): The input SQL query string to sanitize.
        self_call (bool, optional): A flag indicating whether the function is being called
                                    by the model (False) or by a user (True). Defaults to False.

    Returns:
        str or None: Sanitized SQL query if safe, or None if the query is potentially unsafe.
    """
    if not query or not isinstance(query, str):
        return None
    # Remove code block formatting

    LOG.trace('Sanitize Query Input:',query)

    for elements in ['sql', '```', '```', 'trino', '`']:
        query = query.replace(elements, '')
    # Remove any trailing semicolons
    query = query.strip().rstrip(';')

    # Remove any SQL comments
    query = re.sub(r'--.*$', '', query, flags=re.MULTILINE)
    query = re.sub(r'/\*[\s\S]*?\*/', '', query)
    query = re.sub(r'\s+', ' ', query)
    query_lower = query.strip().lower()

    # === SECURITY CHECKS ===
    # Block any data modification or definition language statements
    unsafe_patterns = [
        # DDL operations
        r'\b(create|alter|drop|truncate|rename)\b',
        # DML operations (except SELECT)
        r'\b(insert|update|delete|merge|upsert)\b',
        # DCL operations
        r'\b(grant|revoke|deny)\b',
        # Transaction control
        r'\b(commit|rollback|savepoint)\b',
        # System commands
        r'\b(execute|exec|call|run|system)\b',
        # Any references to system tables or potentially dangerous functions
        r'\b(information_schema\.tables|pg_catalog|sys\.tables)\b',
        r'\b(xp_cmdshell|sp_execute|dblink_exec)\b',
        # Prevent multi-query execution
        r';[^;]*$',
    ]
    # Check if any unsafe patterns are found
    for pattern in unsafe_patterns:
        if re.search(pattern, query_lower):
            if not self_call:
                LOG.info(f"BLOCKED: Potentially unsafe query containing: {pattern}")
            return None

    # === WHITELIST APPROACH ===
    # Ensure query starts with SELECT or WITH (for CTEs)
    if not (query_lower.strip().startswith('select') or
            query_lower.strip().startswith('with') or
            query_lower.strip().startswith('show')):
        if not self_call:
            LOG.info("BLOCKED: Query must start with SELECT, WITH, or SHOW")
        return None

    sanitized_query = query.strip()
    if not self_call:
        LOG.info(f"The sanitized TRINO QUERY - {sanitized_query}")
    return sanitized_query


def replace_vessel_ids_with_count(data):
    """
    Process vessel_ids in query results to prevent context overflow.

    This function recursively processes a data structure (dict or list) to find any
    'vessel_ids' keys that contain lists. For each such key, it adds a count of the
    vessels and trims the list if it contains more than 25 items to avoid context
    overflow errors when processing the results.

    Args:
        data: The data structure to process, which may be a dict, list, or other type.

    Returns:
        The processed data structure with vessel_ids lists potentially trimmed and counts added.
    """
    if isinstance(data, dict):  # If it's a dictionary, iterate through its keys
        for key, value in list(data.items()):
            if key == "vessel_ids" and isinstance(value, list):
                count = len(value)
                data[f"Count(vessel_ids)"] = count  # Add count with new key format
                if count > 25:
                    data[key] = value[:25] + ["... Trimmed Vessel Ids, to avoid context errors"]  # Trim list if necessary
                else:
                    data[key] = value
            else:
                replace_vessel_ids_with_count(value)  # Recursively process nested structures
    elif isinstance(data, list):  # If it's a list, iterate through its elements
        for item in data:
            replace_vessel_ids_with_count(item)
    return data
