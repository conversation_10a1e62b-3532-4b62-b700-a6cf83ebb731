import requests
from utils.LogUtils import LOG

def execute_query(datasource_url, database_name, table_name, query):
    sanitized_query = query.replace("\n", " ").replace("\\n", " ").replace("\\", " ")
    LOG.info(f"Sanitized query = {sanitized_query}")

    url = 'https://global-api.questmarine.app/analytics-execution/query'

    headers = {
        'apiKey': 'JEApWkb70LesmRHQIluTa4BrkVJfhAMubxOpUd0HKlrKttQk8B9wz5zDSLBH',
        'tenantId': '99b092a8-fbf2-432b-8b6a-cdab641ad09d',
        'Content-Type': 'application/json'
    }

    data = {
        "query": sanitized_query
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        print("API call successful!")
        print("Response:")
        print(response.json())
    else:
        print(f"API call failed with status code {response.status_code}")
        print("Error message:")
        print(response.text)

    return response.json()
