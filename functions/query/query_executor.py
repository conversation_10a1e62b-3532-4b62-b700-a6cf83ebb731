import functions.query.mongo_query_executor as mongo_query_executor
import functions.query.dummy_postgres_query_executor as dummy_postgres_query_executor
import functions.query.postgres_query_executor as postgres_query_executor

from utils.LogUtils import LOG

def execute_query(params):
    datasource_url = params["datasource_url"]
    datasource_type = params["datasource_type"]
    database_name = params["database_name"]
    table_name = params["table_name"]
    query = params["query"]

    LOG.info(f"The parameters for query are - {params}")

    if datasource_type == 'MONGO':
        LOG.info("Executing query on Mongo")
        return mongo_query_executor.execute_query(datasource_url, database_name, table_name, query)
    if datasource_type == 'POSTGRES':
        LOG.info("Executing query on Postgres")
        return postgres_query_executor.execute_query(datasource_url, database_name, table_name, query)


