import service.column_service as column_svc
import service.db_service as db_metadata_svc
import service.catalog_service as store_svc
import service.table_service as table_svc

import functions.query.query_executor as query_executor


def extract_function_response(function_name, params):
    if function_name == "list_datasources":
        func_response = list_datasources(params)

    elif function_name == "list_databases_for_datasource":
        func_response = list_databases_for_datasource(params)

    elif function_name == "list_tables_for_database":
        func_response = list_tables_for_database(params)

    elif function_name == "list_columns_for_table":
        func_response = list_columns_for_table(params)

    elif function_name == "execute_query":
        func_response = execute_query(params)

    elif function_name == "plot_data_and_save_file":
        func_response = plot_data_and_save_file(params)

    else:
        func_response = {"error": f"Unknown function {function_name}"}

    return func_response


def list_datasources(params):
    data_sources = store_svc.find_all()
    result = []
    for ds in data_sources:
        result.append({
            "id": ds.id,
            "name": ds.name,
            "description": ds.description,
            "type": ds.type
        })
    return result


def list_databases_for_datasource(params):
    datasource_id = params["datasource_id"]
    db_metadata_list = db_metadata_svc.find_by_store_id(datasource_id)
    result = []
    for ds in db_metadata_list:
        result.append({
            "id": ds.id,
            "name": ds.name,
            "description": ds.description
        })
    return result


def list_tables_for_database(params):
    database_id = params["database_id"]
    tables = table_svc.find_by_db_id(database_id)
    result = []
    for table in tables:
        result.append({
            "id": table.id,
            "name": table.name,
            "description": table.description
        })
    return result


def list_columns_for_table(params):
    table_id = params["table_id"]
    table = table_svc.find_by_id(table_id)
    if table is None:
        raise ValueError(f"No table found for ID = {table_id}")

    columns = column_svc.find_by_table_id(table_id)
    column_list = []
    for column in columns:
        column_list.append({
            "name": column.name,
            "type": column.type,
            "description": column.description
        })

    return column_list


def execute_query(params):
    return query_executor.execute_query(params)


def plot_data_and_save_file(params):
    """
    Execute the code provided by the LLM.
    Returns:
        dict: A dictionary containing local variables after execution.
    """

    code = params["code"]
    exec_result = {}
    exec(code, exec_result)

    return exec_result["file_path"]
