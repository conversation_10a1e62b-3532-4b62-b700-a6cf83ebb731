import json

import dal.few_shot_examples_dal as few_shot_dal
import dal.answer_templates_dal as answer_templates_dal
import functions.query.trino_query_executor as trino_query_executor
import service.catalog_service as catalog_svc
import service.column_service as column_svc
import service.db_service as db_metadata_svc
import service.table_service as table_svc
import dal.tenant_dal as tenant_dal
from utils.LogUtils import LOG
from utils.Tiktoken import execution_timer
from functools import lru_cache


def extract_function_response(function_name, params):
    if function_name == "list_trino_tenants":
        func_response = list_trino_tenants(params)

    elif function_name == "list_trino_catalogs":
        func_response = list_trino_catalogs(params)

    elif function_name == "list_trino_databases_for_catalog":
        func_response = list_trino_databases_for_catalog(params)

    elif function_name == "list_trino_tables_for_database":
        func_response = list_trino_tables_for_database(params)

    elif function_name == "list_trino_columns_for_table":
        func_response = list_trino_columns_for_table(params)

    elif function_name == "execute_trino_query":
        func_response = execute_trino_query(params)

    elif function_name == "plot_data_and_save_file":
        func_response = plot_data_and_save_file(params)

    elif function_name == "get_schema_metadata":
        func_response = get_schema_metadata(params)

    else:
        func_response = {"error": f"Unknown function {function_name}"}

    return func_response


def list_trino_tenants(tenant_name):
    tenant = tenant_dal.find_by_tenant_name(tenant_name)
    return tenant


def list_trino_catalogs(tenant_name):
    # tenant_name = params["tenant_name"]
    data_sources = catalog_svc.find_all(tenant_name)
    result = []
    for ds in data_sources:
        result.append({
            "name": ds.name,
            "description": ds.description,
            "connector_type": ds.connector_type,
            # "tenant_name": ds.tenant_name
        })
    return result


def list_trino_databases_for_catalog(params):
    trino_catalog_name = params["trino_catalog_name"]
    tenant_name = params["tenant_name"]
    db_metadata_list = db_metadata_svc.find_by_catalog_name(trino_catalog_name, tenant_name)
    result = []
    for ds in db_metadata_list:
        result.append({
            "name": ds.name,
            "description": ds.description,
            # "tenant_name": ds.tenant_name
        })
    return result


def list_trino_tables_for_database(params):
    trino_db_name = params["trino_db_name"]
    tenant_name = params["tenant_name"]
    tables = table_svc.find_by_db_name(trino_db_name, tenant_name)
    result = []
    for table in tables:
        result.append({
            "name": table.name,
            "description": table.description,
            # "tenant_name": table.tenant_name
        })
    return result


def list_trino_columns_for_table(params):
    trino_table_name = params["trino_table_name"]
    tenant_name = params["tenant_name"]

    columns = column_svc.find_by_table_name(trino_table_name, tenant_name)
    column_list = []
    for column in columns:
        column_list.append({
            "name": column.name,
            "type": column.type,
            "description": column.description,
            # "tenant_name": column.tenant_name
        })

    return column_list


def execute_trino_query(params):
    return trino_query_executor.sql_query_executor(params["query"])


def plot_data_and_save_file(params):
    """
    Execute the code provided by the LLM.
    Returns:
        dict: A dictionary containing local variables after execution.
    """

    code = params["code"]
    exec_result = {}
    exec(code, exec_result)

    return exec_result["file_path"]


@lru_cache(maxsize=10)
def get_schema_metadata(tenant_name):
    LOG.info(f"inside get schema metadata")
    tenant = list_trino_tenants(tenant_name)
    trino_catalogs = list_trino_catalogs(tenant_name)
    for trino_catalog in trino_catalogs:
        trino_databases = list_trino_databases_for_catalog({
            "trino_catalog_name": trino_catalog["name"],
            "tenant_name": tenant.tenant_name
        })
        for trino_database in trino_databases:
            trino_tables = list_trino_tables_for_database({
                "trino_db_name": trino_database["name"],
                "tenant_name": tenant.tenant_name
            })
            for trino_table in trino_tables:
                trino_columns = list_trino_columns_for_table({
                    "trino_table_name": trino_table["name"],
                    "tenant_name": tenant.tenant_name
                })
                trino_table["columns"] = trino_columns
            trino_database["tables"] = trino_tables
        trino_catalog["databases"] = trino_databases

    return trino_catalogs


@execution_timer
def get_similar_few_shot_examples(user_question: str):
    similar_examples = few_shot_dal.get_similar_few_shot_examples(user_question=user_question)
    examples_list = []
    for example in similar_examples:
        examples_list.append({
            "tenant_name": example.tenant_name,
            "question": example.question,
            "expected_trino_query": example.expected_trino_query,
            "explanation": example.explanation
        })

    beautified_json = json.dumps(examples_list, indent=2)

    return beautified_json


@execution_timer
def get_similar_answer_templates(question_template: str):
    similar_examples = answer_templates_dal.get_similar_answer_templates(question_template, 1)
    if similar_examples:
        return similar_examples[0].response_template
    else:
        return ""
