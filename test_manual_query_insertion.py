#!/usr/bin/env python3
"""
Test script to manually test query insertion functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from config.app_config import Config
from models.meta_data import db
from dal.question_query_history_dal import create_question_query_entry, QuestionQueryManager

def create_test_app():
    """Create a test Flask app with database configuration"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def test_manual_query_execution():
    """Test manual query execution and insertion"""
    print("🧪 Testing manual query execution...")
    
    app = create_test_app()
    with app.app_context():
        try:
            # Import the query executor
            from functions.query.trino_query_executor import sql_query_executor
            
            # Test a simple query
            test_query = "SELECT 1 as test_column"
            test_question = "Manual test question for query execution"
            
            print(f"📤 Executing query: {test_query}")
            print(f"📝 Question: {test_question}")
            
            # This should trigger the create_question_query_entry function
            result = sql_query_executor.invoke({
                "query": test_query,
                "question": test_question
            })
            
            print(f"📋 Query result: {result}")
            print("✅ Manual query execution completed!")
            
            return True
            
        except Exception as e:
            print(f"❌ Manual query execution failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_question_query_manager_methods():
    """Test QuestionQueryManager methods"""
    print("\n🧪 Testing QuestionQueryManager methods...")
    
    app = create_test_app()
    with app.app_context():
        try:
            manager = QuestionQueryManager()
            
            # Test upsert
            test_question = "How many vessels are in the fleet?"
            test_queries = ["SELECT COUNT(*) FROM vessels", "SELECT vessel_name FROM vessels"]
            
            result = manager.upsert_question_with_queries(test_question, test_queries)
            print(f"✅ Upsert result: {result}")
            
            # Test similarity search
            search_question = "How many ships are there?"
            similar_queries, similar_question = manager.get_queries_for_question(search_question)
            
            print(f"🔍 Search question: {search_question}")
            print(f"📝 Similar question: {similar_question}")
            print(f"📋 Similar queries: {similar_queries}")
            
            return True
            
        except Exception as e:
            print(f"❌ QuestionQueryManager test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_trino_connection():
    """Test Trino connection"""
    print("\n🧪 Testing Trino connection...")
    
    try:
        from functions.query.trino_query_executor import pool
        
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchall()
            print(f"✅ Trino connection successful! Result: {result}")
            return True
            
    except Exception as e:
        print(f"❌ Trino connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_service():
    """Test LLM service components"""
    print("\n🧪 Testing LLM service components...")
    
    app = create_test_app()
    with app.app_context():
        try:
            # Test schema metadata retrieval
            import functions.TrinoFunctionImpl as trino_func_svc
            
            schema = trino_func_svc.get_schema_metadata("stage")
            print(f"📋 Schema metadata retrieved: {len(str(schema))} characters")
            
            # Test few-shot examples
            few_shot_examples = trino_func_svc.get_similar_few_shot_examples("How many vessels are there?")
            print(f"📝 Few-shot examples retrieved: {len(few_shot_examples)} characters")
            
            # Test answer templates
            response_template = trino_func_svc.get_similar_answer_templates("How many vessels are there?")
            print(f"📄 Response template retrieved: {len(response_template)} characters")
            
            print("✅ LLM service components working!")
            return True
            
        except Exception as e:
            print(f"❌ LLM service test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main test function"""
    print("🚀 Starting manual query insertion tests...")
    print("=" * 60)
    
    # Test 1: Basic QuestionQueryManager functionality
    test1_result = test_question_query_manager_methods()
    
    # Test 2: Trino connection
    test2_result = test_trino_connection()
    
    # Test 3: LLM service components
    test3_result = test_llm_service()
    
    # Test 4: Manual query execution
    test4_result = test_manual_query_execution()
    
    print("\n🎯 Test Results Summary:")
    print(f"   QuestionQueryManager: {'✅' if test1_result else '❌'}")
    print(f"   Trino Connection: {'✅' if test2_result else '❌'}")
    print(f"   LLM Service Components: {'✅' if test3_result else '❌'}")
    print(f"   Manual Query Execution: {'✅' if test4_result else '❌'}")
    
    if all([test1_result, test2_result, test3_result, test4_result]):
        print("\n✅ All tests passed! The system should be working correctly.")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")
    
    print("\n✅ Manual tests completed!")

if __name__ == "__main__":
    main()
