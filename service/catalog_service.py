import dal.catalog_dal as dal
from models.meta_data import TrinoCatalogMetaData
from utils.LogUtils import LOG


def create(catalog: TrinoCatalogMetaData):
    dal.create(catalog)
    LOG.info(f"CatalogMetaData created successfully - {catalog}")
    return catalog


def find_all(tenant_name: str):
    return dal.find_all(tenant_name)


def find_by_catalog_name(name: str, tenant_name: str):
    return dal.find_by_catalog_name(name, tenant_name)


def update(catalog: TrinoCatalogMetaData):
    dal.update(catalog)
    LOG.info(f"CatalogMetaData updated successfully - {catalog}")
    return catalog


def delete(catalog: TrinoCatalogMetaData):
    dal.delete(catalog)
    LOG.info(f"CatalogMetaData deleted successfully - {catalog}")
