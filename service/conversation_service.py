import uuid
import time

from langchain_core.messages import HumanMessage, AIMessage

import dal.conversation_dal as conversation_dal
import dal.tenant_dal as tenant_dal
import service.llm.base_openai_assistant_service as base_ai_svc
import service.llm.openai_assistant_ai_service as ai_svc
import service.llm.new_langchain_ai_service as lang_ai_svc
import service.llm.openai_assistant_streaming_service as ai_stream_svc
from dal.conversation_dal import get_chat_history
from service import user_question_service
from models.conversation_meta_data import Conversation
from utils.LogUtils import LOG
from utils.PaginationUtils import Pageable
from models.enums import SortOrder
from utils.QuestionDecorator import save_first_conversation_message, convert_messages
from queue import Queue
from config.app_config import Config


def create_new_conversation(user_id: str, tenant_name: str, module_identifier: str, entity_type: str, entity_id: str, entity_name: str):
    conversation_id = str(uuid.uuid4())
    LOG.info(f"New Conversation ID : {conversation_id}")

    new_conversation = Conversation(
        id=conversation_id,
        user_id=user_id,
        tenant_name=tenant_name,
        module_identifier=module_identifier,
        entity_type=entity_type,
        entity_id=entity_id,
        entity_name=entity_name
    )
    conversation_dal.create(new_conversation)
    LOG.info(f"Conversation started successfully - {new_conversation}")
    return new_conversation


def process_question(user_id: str, conversation_id: str, tenant_name: str, question: str):
    tenant = tenant_dal.find_by_tenant_name(tenant_name)
    conversation = get_and_authorize_conversation(user_id, tenant_name, conversation_id)

    LOG.info("Saving the user question")
    user_question_service.save_user_question(user_id, question, conversation_id, conversation.module_identifier)

    LOG.info(f"Using ConversationID = {conversation.id} for answering question - {question}")
    question_metadata = save_first_conversation_message(conversation, question, tenant, conversation_id)
    return question_metadata, conversation.id


def answer_question(user_id: str, conversation_id: str, tenant_name: str, question: str):
    question_metadata, thread_id = process_question(user_id, conversation_id, tenant_name, question)
    res = lang_ai_svc.answerQuestion(question_metadata + question, tenant_name, conversation_id)
    return res


def answer_question_stream(user_id: str, conversation_id: str, tenant_name: str, question: str, queue: Queue):
    from flask_app import app
    with app.app_context():
        if Config.SEND_EVENT_DATA_TO_OUTPUT_STREAM:
            queue.put(f"{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())} => IN CONVERSATION SERVICE \n")
        else:
            LOG.info("IN CONVERSATION SERVICE")

        question_metadata, thread_id = process_question(user_id, conversation_id, tenant_name, question)
        ai_stream_svc.answerQuestionStream(question_metadata + question, thread_id, queue)


def find_by_user_and_tenant(user_id: str, tenant_name: str, pageable: Pageable):
    conversations, total = conversation_dal.find_by_user_and_tenant(user_id, tenant_name, pageable)
    return conversations, total


def get_messages(user_id: str, tenant_name: str, conversation_id: str, pageable: Pageable):
    conversation = get_and_authorize_conversation(user_id, tenant_name, conversation_id)

    LOG.info(f"Using THREADID = {conversation.openai_thread_id} for previous conversation")
    messages = base_ai_svc.get_all_messages_for_thread(conversation.openai_thread_id)

    total = len(messages)
    start_index = (pageable.page_number - 1) * pageable.page_size
    end_index = start_index + pageable.page_size
    paginated_messages = messages[start_index:end_index]

    return paginated_messages, total


def get_messages_from_mongo(conversation_id: str, pageable: Pageable):
    chat_message_history = get_chat_history(conversation_id)
    messages = chat_message_history.messages
    filtered_messages = [msg for msg in messages if isinstance(msg, (HumanMessage, AIMessage))]
    formatted_messages = convert_messages(filtered_messages)
    total = len(formatted_messages)
    start_index = (pageable.page_number - 1) * pageable.page_size
    end_index = min(start_index + pageable.page_size, total)
    paginated_messages = formatted_messages[start_index:end_index]

    return paginated_messages, total


def get_and_authorize_conversation(user_id: str, tenant_name: str, conversation_id: str):
    """
    This function fetches a conversation from the database using the provided `conversation_id`.
    It checks whether the conversation exists and if the provided `user_id` and `tenant_name` match
    the conversation's owner information.

    Args:
        user_id (str): The ID of the user requesting access to the conversation.
        tenant_name (str): The name of the tenant to which the user belongs.
        conversation_id (str): The unique identifier for the conversation.

    Returns:
        conversation: The conversation object corresponding to the given `conversation_id`.
    """
    conversation = conversation_dal.find_by_id(conversation_id)
    if conversation is None:
        raise ValueError("Conversation ID not found")

    if conversation.user_id != user_id or conversation.tenant_name != tenant_name:
        raise ValueError("Unauthorized")

    return conversation


def clear_conversation(user_id: str, tenant_name: str, excluded_ids: [str]):
    threadIds = conversation_dal.get_thread_ids(user_id, tenant_name, excluded_ids)
    conversation_dal.delete_conversations_not_in_excluded_ids(user_id, tenant_name, excluded_ids)

    LOG.info(f"Deleting previous conversation threads for that user = {user_id} and tenant = {tenant_name}")
    #base_ai_svc.delete_threads(threadIds)


# def delete_conversation(user_id: str, tenant_name: str, conversation_id: str):
#     conversation = get_and_authorize_conversation(user_id, tenant_name, conversation_id)
#
#     base_ai_svc.delete_threads([conversation.openai_thread_id])
#     conversation_dal.delete(conversation)


def delete_conversation(user_id: str, tenant_name: str, conversation_id: str):
    conversation = get_and_authorize_conversation(user_id, tenant_name, conversation_id)
    chat_message_history = get_chat_history(conversation_id)
    chat_message_history.clear()
    conversation_dal.delete(conversation)


def get_recent_conversation(user_id: str, tenant_name: str):
    pageable_conversations = Pageable(
        page_number=1,
        page_size=1,
        sort_order=SortOrder.DESC,
        sort_by="createdAt"
    )
    conversations, total = conversation_dal.find_by_user_and_tenant(user_id, tenant_name, pageable_conversations)

    if not conversations or total == 0:
        return None

    recent_conversation = conversations[0]
    return recent_conversation
