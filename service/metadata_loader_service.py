from migration.events_timeline_metadata import metadata as EVENTS_META_DATA
from migration.portfolio_mongo_metadata import metadata as PORTFOLIO_META_DATA
from utils.LogUtils import LOG


def load_all_metadata():
    """Load and merge metadata from multiple Python files."""
    combined_metadata = {"catalogs": []}
    combined_metadata["catalogs"].extend(EVENTS_META_DATA["catalogs"])
    combined_metadata["catalogs"].extend(PORTFOLIO_META_DATA["catalogs"])

    LOG.info(f"Combined metadata: {combined_metadata}")
    return combined_metadata


def replace_placeholders(metadata, tenant_name):
    """Recursively replace placeholders in metadata."""
    if isinstance(metadata, list):
        return [replace_placeholders(item, tenant_name) for item in metadata]
    elif isinstance(metadata, dict):
        return {
            key: replace_placeholders(value, tenant_name)
            if isinstance(value, (dict, list, str))
            else value
            for key, value in metadata.items()
        }
    elif isinstance(metadata, str):
        return metadata.replace("{TENANT_NAME}", tenant_name)
    else:
        return metadata


def apply_tenant_customizations(metadata, tenant):
    """Apply tenant-specific exclude/include rules."""
    LOG.info(f"Applying tenant-specific rules for {tenant.tenant_name}")

    catalogs_to_keep = []
    for catalog in metadata["catalogs"]:
        if catalog["name"] not in tenant.exclude_catalogs:
            catalogs_to_keep.append(catalog)

    metadata["catalogs"] = catalogs_to_keep

    for catalog in metadata["catalogs"]:
        filtered_databases = []
        for db in catalog["databases"]:
            if db["name"] not in tenant.exclude_databases:
                filtered_databases.append(db)
        catalog["databases"] = filtered_databases

        for db in catalog["databases"]:
            filtered_tables = []
            for table in db["tables"]:
                if table["name"] not in tenant.exclude_tables:
                    filtered_tables.append(table)
            db["tables"] = filtered_tables

    LOG.info(f"Metadata after tenant customization: {metadata}")
    return metadata
