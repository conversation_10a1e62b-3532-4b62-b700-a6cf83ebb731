import uuid

import dal.column_dal as dal
from models.meta_data import TrinoColumnMetaData
from utils.LogUtils import LOG


def create(col_meta: TrinoColumnMetaData):
    dal.create(col_meta)
    LOG.info(f"ColumnMetaData created successfully - {col_meta}")
    return col_meta


def find_all(tenant_name: str):
    return dal.find_all(tenant_name)


def find_by_id(id: uuid, tenant_name: str):
    return dal.find_by_id(id, tenant_name)


def update(col_meta: TrinoColumnMetaData):
    dal.update(col_meta)
    LOG.info(f"ColumnMetaData updated successfully - {col_meta}")
    return col_meta


def delete(col_meta: TrinoColumnMetaData):
    dal.delete(col_meta)
    LOG.info(f"ColumnMetaData deleted successfully - {col_meta}")


def find_by_table_name(trino_table_name: str, tenant_name: str):
    return dal.find_by_table_name(trino_table_name=trino_table_name, tenant_name=tenant_name)
