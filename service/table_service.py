import dal.table_dal as dal
from models.meta_data import TrinoTableMetaData
from utils.LogUtils import LOG


def create(table_meta: TrinoTableMetaData):
    dal.create(table_meta)
    LOG.info(f"TableMetaData created successfully - {table_meta}")
    return table_meta


def find_all(tenant_name: str):
    return dal.find_all(tenant_name)


def find_by_table_name(name: str, tenant_name: str):
    return dal.find_by_table_name(name, tenant_name)


def update(table_meta: TrinoTableMetaData):
    dal.update(table_meta)
    LOG.info(f"TableMetaData updated successfully - {table_meta}")
    return table_meta


def delete(db_meta: TrinoTableMetaData):
    dal.delete(db_meta)
    LOG.info(f"TableMetaData deleted successfully - {db_meta}")


def find_by_db_name(trino_db_name: str, tenant_name: str):
    return dal.find_by_db_name(trino_db_name=trino_db_name, tenant_name=tenant_name)
