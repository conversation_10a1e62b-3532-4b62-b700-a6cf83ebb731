import service.llm.openai_question_recommendation_service as ai_question_recommendation_svc
from constants.question_recommendation_constants import CONVERSATION_HISTORY_THRESHOLD, MINIMUM_USER_HISTORY_THRESHOLD, MAXIMUM_USER_HISTORY_THRESHOLD
from dal import question_recommendation_dal
from models.enums import SortOrder
from utils.LogUtils import LOG
from utils.PaginationUtils import Pageable


def get_recommended_questions_for_chat(user_id: str, module_identifier: str, conversation_id: str=None, entity_type: str=None):
    if conversation_id:
        # Fetch recommended questions for a user based on their conversation.
        return get_recommended_questions_for_user_and_conversation(user_id, conversation_id, module_identifier, entity_type)
    # Fetch recommended questions for a user based on their overall history.
    return get_recommended_questions_for_user(user_id, module_identifier, entity_type)


def get_recommended_questions_for_user_and_conversation(user_id: str, conversation_id: str, module_identifier: str, entity_type: str=None):
    pageable_conversation = Pageable(page_number=1, page_size=CONVERSATION_HISTORY_THRESHOLD, sort_order=SortOrder.DESC, sort_by="created_on")

    # Fetch questions from the current conversation
    questions = question_recommendation_dal.find_all_recent_questions(user_id, module_identifier, pageable_conversation, conversation_id, entity_type)
    if len(questions) < CONVERSATION_HISTORY_THRESHOLD:
        LOG.info(f"Conversation {conversation_id} has fewer than {CONVERSATION_HISTORY_THRESHOLD} questions. No recommendations generated.")
        return []
    user_questions = [ques.question for ques in questions[:CONVERSATION_HISTORY_THRESHOLD]]
    LOG.info(f"Fetched {len(user_questions)} questions from conversation {conversation_id}.")

    # Generate recommendations using AI service
    LOG.info(f"Generating recommendations for user_id: {user_id}")
    recommendations = ai_question_recommendation_svc.get_question_recommendations(user_questions, module_identifier, entity_type)
    return recommendations


def get_recommended_questions_for_user(user_id: str, module_identifier: str, entity_type: str=None):
    pageable_user = Pageable(page_number=1, page_size=MAXIMUM_USER_HISTORY_THRESHOLD, sort_order=SortOrder.DESC, sort_by="created_on")

    # Fetch questions from all user history
    questions = question_recommendation_dal.find_all_recent_questions(user_id, module_identifier, pageable_user, None, entity_type)
    if len(questions) < MINIMUM_USER_HISTORY_THRESHOLD:
        LOG.info(f"User {user_id} has fewer than {MINIMUM_USER_HISTORY_THRESHOLD} questions across all conversations. No recommendations generated.")
        return []
    user_questions = [ques.question for ques in questions[:MAXIMUM_USER_HISTORY_THRESHOLD]]
    LOG.info(f"Fetched {len(user_questions)} questions from user {user_id} across all conversations.")

    # Generate recommendations using AI service
    LOG.info(f"Generating recommendations for user_id: {user_id}")
    recommendations = ai_question_recommendation_svc.get_question_recommendations(user_questions, module_identifier, entity_type)
    return recommendations
