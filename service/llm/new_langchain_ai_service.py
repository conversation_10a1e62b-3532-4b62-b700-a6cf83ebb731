"""Langchain AI Service Module

This module provides functionality for answering natural language questions using
Langchain's agent framework with Trino SQL query execution capabilities. It handles
the entire process from receiving a question to returning a formatted answer.

The module uses a structured chat agent with a zero-shot react description approach,
which allows the LLM to reason through complex questions by breaking them down into
steps and executing SQL queries as needed.

Key components:
- answerQuestion: Main function to process a user question and generate an answer
- chat_react_function: Handles the interaction with the Langchain agent
- replace_enhanced_timeline: Utility function for text processing

The module integrates with various services including:
- Trino database for executing SQL queries
- LLM providers through the NewLLMProvider module
- Conversation history management
- Few-shot examples and response templates for improved answers
"""

import warnings
from datetime import datetime

from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.agents import initialize_agent, AgentType
from langchain_core.messages import AIMessage, trim_messages
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder

import constants.PromptNewConstants as promptNewConstants
import functions.TrinoFunctionImpl as trino_func_svc
from constants.PromptNewConstants import phrases_to_ignore, failure_phrases
from dal.conversation_dal import get_chat_history
from functions.query.trino_query_executor import *
from llm.NewLLMProvider import get_llm_object
from utils.LogUtils import LOG
from utils.Tiktoken import execution_timer
from dal.question_query_history_dal import get_queries_for_question

warnings.filterwarnings("ignore")


def answerQuestion(question, tenant_name, conversation_id):
    """
    Process a natural language question and generate an answer using LLM and SQL queries.

    This function orchestrates the entire question-answering process:
    1. Initializes the LLM model and retrieves conversation history
    2. Gathers schema metadata, response templates, and few-shot examples
    3. Constructs a prompt with all necessary context
    4. Invokes the LLM agent to generate a response
    5. Updates the conversation history with the new response

    Args:
        question (str): The natural language question from the user
        tenant_name (str): The tenant identifier for database schema selection
        conversation_id (str): The ID of the conversation for history tracking

    Returns:
        str: The generated answer to the user's question
    """
    model = get_llm_object(Config.LLM_IN_USE)
    chat_message_history = get_chat_history(conversation_id)
    tools = [parallel_sql_query_executor]
    schema = trino_func_svc.get_schema_metadata(tenant_name)
    LOG.info(f"Schema Received for : {tenant_name} :\n{schema}")
    response_template = trino_func_svc.get_similar_answer_templates(question)
    few_shot_examples = trino_func_svc.get_similar_few_shot_examples(question)
    few_shot_examples = few_shot_examples.replace('\\"enhanced-timeline\\"', "enhanced_timeline")
    few_shot_question_based, similarity_question = get_queries_for_question(question)
    few_shot_question_based = str(few_shot_question_based).replace('\"enhanced-timeline\"', "enhanced_timeline")
    LOG.info(f"Few shots examples are : {few_shot_examples}")
    LOG.info(f"Response Template : {response_template}")
    LOG.info(f"Example Queries : {few_shot_question_based}")

    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", promptNewConstants.PROMPT_SYS_MSG),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", promptNewConstants.PROMPT),
            ("placeholder", "{agent_scratchpad}"),
        ]
    )

    chat_history = trim_messages(chat_message_history.messages, strategy="last", token_counter=len,
                                 max_tokens=40, start_on="human", end_on=("human", "tool", "ai"), include_system=False)

    input_values = {"schema": schema, "response_template": response_template, "few_shot_examples": few_shot_examples,
                    "question": question, "chat_history": chat_history,
                    "date_time": str(datetime.now(pytz.utc).strftime("%Y-%m-%d %H:%M:%S %Z")),
                    "few_shot_question_based": few_shot_question_based, "similarity_question": similarity_question}

    input_values = replace_enhanced_timeline(input_values)
    res = chat_react_function(prompt, model, tools, input_values)
    chat_message_history.add_ai_message(
        AIMessage(content=res, additional_kwargs={"timestamp": datetime.now().strftime("%H:%M:%S")}))
    return res


@execution_timer
def chat_react_function(prompt, model, tools, input_values):
    """
    Execute the LLM agent with the provided prompt, model, tools, and input values.

    This function initializes a Langchain agent with the STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION
    approach, which allows the LLM to reason through complex questions step by step.
    It also handles post-processing of the response, including removing unwanted phrases
    and handling failure cases.

    Args:
        prompt (ChatPromptTemplate): The prompt template to use for the agent
        model: The LLM model to use for generating responses
        tools (list): The list of tools available to the agent
        input_values (dict): The input values to populate the prompt template

    Returns:
        str: The processed response from the agent

    Note:
        This function is decorated with @execution_timer to track performance metrics.
    """
    try:
        LOG.info(f"Invoked STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION")
        agent = initialize_agent(
            tools=tools,
            llm=model,
            agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True,
            handle_parsing_errors=True,
            max_execution_time=45,
            max_iterations=3,
            early_stopping_method='generate'
        )
        complete_prompt = prompt.invoke(input_values)
        response = agent.invoke(complete_prompt)
        LOG.debug(f"Response : {response['output']}")
        response = response['output']

        # Remove phrases to ignore
        for phrase in phrases_to_ignore:
            response = response.replace(phrase, '')

        # Check for failure phrases
        if any(failure_phrase in response for failure_phrase in failure_phrases):
            response = promptNewConstants.default_failure_msg

        return response

    except Exception as e:
        LOG.error(f"Error in generating response by STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION : {e}")
        return promptNewConstants.default_failure_msg


def replace_enhanced_timeline(data):
    """
    Recursively replace 'enhanced-timeline' with 'enhanced_timeline' in all strings within a data structure.
    Enhanced-timeline is replaced to avoid escape sequence errors.

    This utility function handles different data types (dict, list, str) and recursively
    processes nested structures to ensure all occurrences of 'enhanced-timeline' are
    replaced with 'enhanced_timeline' (hyphen to underscore).

    Args:
        data: The data structure to process, which may be a dict, list, str, or other type

    Returns:
        The processed data structure with all occurrences of 'enhanced-timeline' replaced
    """
    if isinstance(data, dict):
        return {key: replace_enhanced_timeline(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [replace_enhanced_timeline(item) for item in data]
    elif isinstance(data, str):
        return data.replace("enhanced-timeline", "enhanced_timeline")
    return data
