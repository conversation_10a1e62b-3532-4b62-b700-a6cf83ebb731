import time
import openai
from config.app_config import Config
from models.llm_response import Response, File
from utils.LogUtils import LOG
import service.llm.base_openai_assistant_service as base_ai_svc
from utils.Tiktoken import execution_timer
ASSISTANT_ID = Config.OPENAI_ASSISTANT_ID
THREAD_ID = Config.OPENAI_ASSISTANT_THREAD_ID


def answerQuestionWithSystemThread(question: str):
    LOG.info(f"Answering question = {question} using threadID - {THREAD_ID}")
    return answerQuestion(question, THREAD_ID)


def answerQuestion(question: str, thread_id: str):
    LOG.info(f"Using THREAD - {thread_id} for answering")

    base_ai_svc.cancel_active_runs(thread_id)
    total_llm_time = 0
    request_start_time = time.time()

    # Add the user's message to the thread
    openai.beta.threads.messages.create(
        thread_id=thread_id,
        role="user",
        content=base_ai_svc.get_question_with_few_shot_examples_with_template(question)
    )

    llm_start_time = time.time()
    run = openai.beta.threads.runs.create(
        thread_id=thread_id,
        assistant_id=ASSISTANT_ID
    )

    max_retries = 300  # 30 seconds max with 0.1s sleep
    for attempt in range(max_retries):
        run_status = openai.beta.threads.runs.retrieve(
            thread_id=thread_id,
            run_id=run.id
        )

        attempt = attempt + 1
        LOG.info(f"Attempt {attempt + 1}/{max_retries}: The run status = {run_status.status}")

        if run_status.status == 'completed':
            return handle_completion(thread_id, total_llm_time, request_start_time)

        elif run_status.status == 'requires_action':
            LOG.info("Tool Calling In-progress")
            llm_end_time = time.time()
            llm_duration = llm_end_time - llm_start_time
            total_llm_time += llm_duration

            handle_required_action(run_status, thread_id, run.id)
            llm_start_time = time.time()

        elif run_status.status in ['queued', 'in_progress']:
            LOG.info("Run is queued or in progress. Waiting...")

        elif run_status.status == 'cancelling':
            LOG.warning("Run is being cancelled. Waiting for final status...")

        elif run_status.status in ['cancelled', 'failed', 'expired']:
            LOG.error(f"Run ended with status: {run_status.status}")
            return {"error": f"Run ended with status: {run_status.status}"}

        elif run_status.status == 'incomplete':
            LOG.error("Run is incomplete. Starting a new run.")
            run = openai.beta.threads.runs.create(
                thread_id=thread_id,
                assistant_id=ASSISTANT_ID
            )

        else:
            LOG.error(f"Unknown run status: {run_status.status}")
            return {"error": f"Unknown run status: {run_status.status}"}

        time.sleep(0.1)  # Short sleep to prevent tight looping

    LOG.error("Max retries reached. The operation did not complete in time.")
    return {"error": "Operation timed out"}


def handle_completion(thread_id, total_llm_time, request_start_time):
    LOG.info("Run completed successfully")
    messages = openai.beta.threads.messages.list(thread_id=thread_id)
    total_request_time = time.time() - request_start_time
    LOG.info(f"The final response is {messages.data[0]}")
    return parse_final_llm_response(messages.data[0], total_llm_time, total_request_time)


@execution_timer
def handle_required_action(run_status, thread_id, run_id):
    LOG.info(f"AI REQUESTED - {len(run_status.required_action.submit_tool_outputs.tool_calls)} tool calls")

    tool_outputs = base_ai_svc.process_tool_calls(run_status.required_action.submit_tool_outputs.tool_calls)

    openai.beta.threads.runs.submit_tool_outputs(
        thread_id=thread_id,
        run_id=run_id,
        tool_outputs=tool_outputs
    )


'''
Refering 
https://platform.openai.com/docs/assistants/tools/code-interpreter/reading-images-and-files-generated-by-code-interpreter
'''


def parse_final_llm_response(data, total_llm_time, total_request_time):
    response = Response()
    response.llm_processing_time = total_llm_time
    response.total_processing_time = total_request_time

    for content_block in data.content:
        if content_block.type == "text":
            text = content_block.text.value
            response.text = text
            LOG.info(f"TEXT - {text}")
        elif content_block.type == "image_file":
            response.file = File()
            file_content = get_file_content(content_block.image_file.file_id)
            response.file.content = file_content
            response.file.content_bytes = file_content
            LOG.info(f"IMAGE = {len(file_content)}")

    return response


def get_file_content(file_id):
    response = openai.files.content(file_id)
    return response.content

