import os
from constants.app_constants import APP_MODE_LANGCHAIN, APP_MODE_OPEN_AI_ASSISTANT

import service.llm.langchain_ai_service as lanchainAISvc
import service.llm.openai_assistant_ai_service as openAIAssistantSvc

llm_mode = os.getenv('LLM_MODE')


def answerQuestion(question):
    if llm_mode == APP_MODE_LANGCHAIN:
        return lanchainAISvc.answerQuestion(question)
    elif llm_mode == APP_MODE_OPEN_AI_ASSISTANT:
        return openAIAssistantSvc.answerQuestion(question)
    else:
        raise ValueError("Invalid LLM_MODE")


