import json
import openai
import functions.TrinoFunctionImpl as trino_func_svc
from config.app_config import Config
from constants.app_constants import MAX_ALLOWED_TOKENS, OPENAI_MODEL
from utils.LogUtils import LOG
from utils.Tiktoken import get_tokens
from utils.tabular_log_utils import log_tabular_data

ASSISTANT_ID = Config.OPENAI_ASSISTANT_ID
THREAD_ID = Config.OPENAI_ASSISTANT_THREAD_ID

"""This is base service for OPENAI ASSISTANT functions used by Conversation APIs"""


def create_thread():
    thread = openai.beta.threads.create()
    LOG.info(f"Created new thread with ID = {thread.id}")
    return thread


def get_question_with_few_shot_examples_with_template(question: str):
    response_template = trino_func_svc.get_similar_answer_templates(question)
    few_shot_examples = trino_func_svc.get_similar_few_shot_examples(question)

    combined_prompt = f"""
        Answer the question in this response template format only:
        {response_template}

        Few-shot examples:
        {few_shot_examples}

        Now, please answer the following question:
        {question}"""

    LOG.info(f"The question with few shot examples = {combined_prompt}")

    return combined_prompt


def process_tool_calls(tool_calls):
    LOG.info(f"Tool calls are: {tool_calls}")
    tool_outputs = []
    for tool_call in tool_calls:
        tool_call_id = tool_call.id
        function_name = tool_call.function.name
        function_args = json.loads(tool_call.function.arguments)

        log_tabular_data(
            prefix="Extracted tool_call from LLM Response:",
            headers=["Tool Call ID", "Function", "Args"],
            data=[[tool_call_id, function_name, function_args]]
        )

        function_response = trino_func_svc.extract_function_response(function_name, function_args)

        # Get token count for the response
        query_tokens = get_tokens(json.dumps(function_response), OPENAI_MODEL)

        '''
        If query tokens is greater than max allowed tokens then append a response indicating the dataset is too large to process.
        The response instructs the user to refine their question for better results
        '''

        if query_tokens > MAX_ALLOWED_TOKENS:
            LOG.info(f"Query token count: {query_tokens} is more than max allowed tokens: {MAX_ALLOWED_TOKENS}")
            tool_outputs.append({
                "tool_call_id": tool_call_id,
                "output": json.dumps({
                    "message": """
                               Data set is too large so you have to ask the user to refine the question by saying,
                               DataSet is too large and you have to ask the user to refine the question for better and precise results 
                               by providing them some sample question related to context and at last you have to say you want this so that 
                               you can give precise answer.
                               """
                })
            })

        else:
            log_tabular_data(
                prefix="Tool Call Response:",
                headers=["Tool Call ID", "Function", "Args", "Function_Response"],
                data=[[tool_call_id, function_name, function_args, function_response]]
            )
            tool_outputs.append({
                "tool_call_id": tool_call_id,
                "output": json.dumps(function_response)
            })

    return tool_outputs


def get_all_messages_for_thread(thread_id: str):
    messages = openai.beta.threads.messages.list(thread_id=thread_id)
    all_messages = []

    for message in messages.data:
        role = message.role
        content = message.content
        timestamp = message.created_at

        text = next((item.text.value for item in content if item.type == 'text'), None)
        if role == 'user':
            text = text.split("=>")[1].strip()

        all_messages.append({
            'role': role,
            'text': text,
            'timestamp': timestamp
        })

    return all_messages


def cancel_run(thread_id, run_id):
    try:
        cancelled_run = openai.beta.threads.runs.cancel(
            thread_id=thread_id,
            run_id=run_id
        )
        LOG.info(f"Cancelled run {run_id} on thread {thread_id}")
        return cancelled_run
    except openai.OpenAIError as e:
        LOG.error(f"Failed to cancel run {run_id}: {str(e)}")
        return None


def get_active_runs(thread_id):
    runs = openai.beta.threads.runs.list(thread_id=thread_id)
    active_runs = [run for run in runs.data if run.status in ['queued', 'in_progress', 'requires_action', 'incomplete']]
    return active_runs


def cancel_active_runs(thread_id):
    active_runs = get_active_runs(thread_id)

    LOG.info(f"Found {len(active_runs)} ACTIVE RUNS.")
    for active_run in active_runs:
        LOG.info("Found active run for the current thread. So need to cancel it")
        cancel_run(thread_id, active_run.id)
        LOG.info("Active run cancelled successfully")


def delete_threads(threadIds):
    for threadId in threadIds:
        openai.beta.threads.delete(thread_id=threadId)
        LOG.info(f"Thread deleted successfully - {threadId}")
