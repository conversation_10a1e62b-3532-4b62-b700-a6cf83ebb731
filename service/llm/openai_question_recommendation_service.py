import json
import openai

from constants.question_recommendation_constants import AI_RESPONSE_TEMPLATE, AI_QUESTION_RECOMMENDATION_COUNT, AI_QUESTION_RECOMMENDATION_MODEL
from utils.LogUtils import LOG

def get_question_recommendations(user_questions, module_identifier: str, entity_type: str):
    """
        Generate question recommendations using an AI model.
    """
    user_questions_str = '\n'.join(user_questions)
    messages = [
        {
            "role": "user",
            "content": AI_RESPONSE_TEMPLATE.format(
                module_identifier=module_identifier,
                entity_type=entity_type,
                user_questions=user_questions_str,
                num_recommendations=AI_QUESTION_RECOMMENDATION_COUNT
            ),
        }
    ]

    try:
        response = openai.chat.completions.create(
            model=AI_QUESTION_RECOMMENDATION_MODEL,
            messages=messages
        )
        assistant_response = response.choices[0].message.content
        recommendations = json.loads(assistant_response).get("recommended_questions", [])

        if len(recommendations) == AI_QUESTION_RECOMMENDATION_COUNT:
            LOG.info(f"Successfully generated {len(recommendations)} question recommendations.")
            return recommendations
        else:
            LOG.error(f"Invalid response format from AI. Received: {assistant_response}")
            return []

    except Exception as e:
        LOG.error(f"Error generating recommendations: {str(e)}")
        return []