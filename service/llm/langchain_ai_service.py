import json
import time
import traceback

from langchain_core.messages import HumanMessage, ToolMessage

import constants.PromptConstants as promptConstants
import functions.FunctionImpl as funcService
import llm.LLMProvider as llmProvider
from utils.LogUtils import LOG
from utils.tabular_log_utils import log_tabular_data


def answerQuestion(question):
    prompt = promptConstants.getQuestionPrompt(question)
    llm_with_tools = llmProvider.get_llm()
    LOG.info(f"The LLM chosen is {llm_with_tools}")
    return answerPrompt(prompt, llm_with_tools)


"""
If LLM response has 2 tool calls, then we should answer both 
before invoking LLM again

Else it will complain that one of the tool_call_id doesn't have message
"""


def answerPrompt(prompt, llm_with_tools):
    function_calling_in_process = True
    messages = [HumanMessage(prompt)]
    llm_response = llm_with_tools.invoke(messages)
    messages.append(llm_response)
    LOG.info(f"Answer to initial prompt: {llm_response}")

    func_requests_responses = []
    tool_call_stack = []

    # Add initial tool calls to the stack
    if hasattr(llm_response, 'tool_calls'):
        tool_call_stack.extend(llm_response.tool_calls)
    LOG.info(f"Found {len(llm_response.tool_calls)} TOOL CALLS")

    while function_calling_in_process:
        while tool_call_stack:
            tool_call = tool_call_stack.pop()
            try:
                function_name = tool_call["name"]
                function_args = tool_call["args"]
                tool_call_id = tool_call["id"]

                log_tabular_data(
                    prefix="Extracted tool_call from LLM Response:",
                    headers=["Tool Call ID", "Function", "Args"],
                    data=[[tool_call_id, function_name, function_args]]
                )

                func_response = funcService.extract_function_response(function_name, function_args)

                log_tabular_data(
                    prefix="Tool Call Response:",
                    headers=["Tool Call ID", "Function", "Args", "Function_Response"],
                    data=[[tool_call_id, function_name, function_args, func_response]]
                )

                func_requests_responses.append(
                    [tool_call_id, function_name, function_args, func_response]
                )

                messages.append(ToolMessage(json.dumps(func_response), tool_call_id=tool_call_id))

                # Adding delay to avoid overwhelming the tool
                time.sleep(0.5)

            except Exception as e:
                LOG.error(f"Unexpected error: {e}")
                traceback.print_exc()
                function_calling_in_process = False
                break  # Exit the inner while loop in case of an error

        # Invoke LLM with new messages only after all current tool calls are processed
        if not tool_call_stack:
            llm_response = llm_with_tools.invoke(messages)
            messages.append(llm_response)
            LOG.info(f"The LLM Response = {llm_response}")

            # Add new tool calls to the stack
            if hasattr(llm_response, 'tool_calls'):
                tool_call_stack.extend(llm_response.tool_calls)
            LOG.info(f"Found {len(llm_response.tool_calls)} NEW TOOL CALLS")

            # End the function calling process if no new tool calls were found
            if not llm_response.tool_calls:
                function_calling_in_process = False

    return llm_response, func_requests_responses
