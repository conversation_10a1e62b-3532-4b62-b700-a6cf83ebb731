import time
from queue import Queue
import openai
from openai.types.beta.threads import Text
from openai.types.beta.threads.runs import Too<PERSON><PERSON><PERSON>, RunStep
from config.app_config import Config
from utils.LogUtils import LOG
import service.llm.base_openai_assistant_service as base_ai_svc

from typing_extensions import override
from openai import Assistant<PERSON>vent<PERSON>andler

ASSISTANT_ID = Config.OPENAI_ASSISTANT_ID


"""
See - https://platform.openai.com/docs/api-reference/assistants-streaming/events
"""

def log_message(message, queue):
    """Logs message to UI queue if enabled; otherwise, logs to console."""
    if Config.SEND_EVENT_DATA_TO_OUTPUT_STREAM:
        queue.put(f"{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())} => {message} \n")
    else:
        LOG.info(message)


class EventHandler(AssistantEventHandler):

    def __init__(self, thread_id, assistant_id, run_id, queue):
        super().__init__()
        self.thread_id = thread_id
        self.assistant_id = assistant_id
        self.run_id = run_id
        self.run_step = None
        self.queue = queue
        self.output_stream_started = False

    @override
    def on_run_step_created(self, run_step: RunStep) -> None:
        """Callback that is fired when a run step is created"""
        self.run_id = run_step.run_id
        self.run_step = run_step
        log_message("RUN STEP CREATED", self.queue)

    @override
    def on_text_delta(self, delta, snapshot):
        if not self.output_stream_started:
            log_message("OUTPUT STREAMING STARTED", self.queue)
            self.output_stream_started = True
        self.queue.put(delta.value)

    @override
    def on_text_done(self, text: Text) -> None:
        log_message("OUTPUT STREAMING DONE", self.queue)
        self.queue.put(None)

    @override
    def on_exception(self, exception: Exception) -> None:
        self.queue.put(None)

    @override
    def on_tool_call_created(self, tool_call: ToolCall) -> None:
        log_message(f"TOOL CALL CREATED - NAME = {tool_call.function.name}, args = {tool_call.function.arguments}", self.queue)

    @override
    def on_tool_call_done(self, tool_call: ToolCall) -> None:

        log_message(f"ON TOOL CALL DONE CALLED - {tool_call.function.name}", self.queue)
        current_run_status = openai.beta.threads.runs.retrieve(
            thread_id=self.thread_id,
            run_id=self.run_id
        )

        if current_run_status.status == 'requires_action':
            LOG.info(f"AI REQUESTED - {len(current_run_status.required_action.submit_tool_outputs.tool_calls)} tool calls")

            tool_call_outputs = base_ai_svc.process_tool_calls(current_run_status.required_action.submit_tool_outputs.tool_calls)
            log_message(f"TOOL CALL OUTPUT GENERATED - {tool_call.function.name}", self.queue)

            with openai.beta.threads.runs.submit_tool_outputs_stream(
                    thread_id=self.thread_id,
                    run_id=self.run_id,
                    tool_outputs=tool_call_outputs,
                    event_handler=EventHandler(self.thread_id, self.assistant_id, self.run_id, self.queue)
            ) as stream:
                stream.until_done()
        else:
            log_message(f"Status for run in TOOL CALL DONE is {current_run_status.status}", self.queue)


def answerQuestionStream(question: str, thread_id: str, queue: Queue):
    log_message("IN AI SERVICE", queue)
    LOG.info(f"Using THREAD - {thread_id} for answering")
    base_ai_svc.cancel_active_runs(thread_id)

    # Add the user's message to the thread
    openai.beta.threads.messages.create(
        thread_id=thread_id,
        role="user",
        content=base_ai_svc.get_question_with_few_shot_examples_with_template(question)
    )

    handler = EventHandler(thread_id, ASSISTANT_ID, None, queue)
    log_message("START RUN STREAMING", queue)

    with openai.beta.threads.runs.stream(
            thread_id=thread_id,
            assistant_id=ASSISTANT_ID,
            event_handler=handler
    ) as stream:
        stream.until_done()
