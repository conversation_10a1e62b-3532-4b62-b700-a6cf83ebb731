import uuid

import dal.feedback_dal as feedback_dal
import service.conversation_service as conversation_service
from models.conversation_meta_data import UserF<PERSON><PERSON><PERSON>
from utils.PaginationUtils import Pageable
from utils.LogUtils import LOG


def submit_feedback(feedback: UserFeedBack):
    conversation = conversation_service.get_and_authorize_conversation(feedback.user_id, feedback.tenant_name,
                                                                       feedback.conversation_id)

    feedback_dal.create(feedback)
    LOG.info(f"Feedback saved successfully - {feedback}")
    return feedback


def find_feedbacks_by_filters(filters: dict, pageable: Pageable):
    return feedback_dal.find_feedbacks_with_pagination_and_sorting(filters, pageable)


def find_feedbacks_by_user_and_thread(user_id: str, tenant_name: str, conversation_id: str):
    feedbacks = feedback_dal.find_feedbacks_by_user_and_thread(user_id, tenant_name, conversation_id)
    return feedbacks
