import uuid
from dal import user_question_dal
from models.conversation_meta_data import UserQuestion
from utils.LogUtils import LOG

'''
Extracting the actual user question from the formatted question string.
The question string is passed to the LLM in the format:
"[TENANT = (tenant_name, tenant_id)] + QUESTION_METADATA = (some_metadata) => actual_user_question"
To retrieve only the 'actual_user_question' part, we split the string on '=>' and take the second part.
'''


def save_user_question(user_id: str, question: str, conversation_id: str, module_identifier: str):
    new_user_question = UserQuestion(
        id=str(uuid.uuid4()),
        user_id=user_id,
        question=question,
        conversation_id=conversation_id,
        module_identifier=module_identifier
    )
    user_question_dal.create(new_user_question)
    LOG.info(f"User Question saved successfully - {new_user_question}")
    return new_user_question
