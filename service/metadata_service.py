import uuid
from dal import catalog_dal
from dal import db_dal
from dal import table_dal
from dal import column_dal
from dal import tenant_dal
from utils.LogUtils import LOG
from models.meta_data import TrinoColumnMetaData, TrinoTableMetaData, TrinoDBMetaData, TrinoCatalogMetaData, TenantInfo
from service import metadata_loader_service


def insert_tenant(tenant_name, tenant_id):
    """Insert or update tenant in TenantInfo table."""
    tenant_dal.delete_by_tenant_name(tenant_name)
    new_tenant = TenantInfo(tenant_name=tenant_name, tenant_id=tenant_id, )
    tenant_dal.create(new_tenant)
    LOG.info(f"Inserted new TenantInfo: {tenant_name} (ID: {tenant_id})")


def delete_metadata(tenant_name):
    """Delete all data for respective tenant recursively"""
    catalog_dal.delete_by_tenant_name(tenant_name)
    print(f"Deleted metadata for tenant: {tenant_name}")


def insert_metadata(metadata, tenant_name):
    """Insert metadata into the database."""
    for catalog in metadata["catalogs"]:
        catalog_meta = TrinoCatalogMetaData(
            name=catalog["name"],
            description=catalog["description"],
            connector_type=catalog["connector_type"],
            tenant_name=tenant_name
        )
        catalog_dal.create(catalog_meta)
        LOG.info("Catalog inserted")

        for db in catalog["databases"]:
            db_meta = TrinoDBMetaData(
                name=catalog_meta.name + "." + db["name"],
                trino_catalog_name=catalog["name"],
                description=db["description"],
                tenant_name=tenant_name

            )
            db_dal.create(db_meta)

            for table in db["tables"]:
                table_meta = TrinoTableMetaData(
                    name=db_meta.name + "." + table["name"],
                    trino_db_name=db_meta.name,
                    description=table["description"],
                    tenant_name=tenant_name,
                )
                table_dal.create(table_meta)

                for column in table["columns"]:
                    column_meta = TrinoColumnMetaData(
                        id=str(uuid.uuid4()),
                        name=column["name"],
                        type=column["type"],
                        description=column["description"],
                        trino_table_name=table_meta.name,
                        tenant_name=tenant_name,
                    )
                    column_dal.create(column_meta)

    print(f"Inserted metadata for tenant: {tenant_name}")


def process_metadata(tenant, metadata):
    """Process metadata for a tenant."""
    insert_tenant(tenant.tenant_name, tenant.tenant_id)
    metadata = metadata_loader_service.replace_placeholders(metadata, tenant.tenant_name)
    metadata = metadata_loader_service.apply_tenant_customizations(metadata, tenant)
    delete_metadata(tenant.tenant_name)
    insert_metadata(metadata, tenant.tenant_name)
