import dal.db_dal as dal
from models.meta_data import TrinoDBMetaData
from utils.LogUtils import LOG


def create(db_meta: TrinoDBMetaData):
    dal.create(db_meta)
    LOG.info(f"DBMetaData created successfully - {db_meta}")
    return db_meta


def find_all(tenant_name):
    return dal.find_all(tenant_name)


def find_by_db_name(name: str, tenant_name: str):
    return dal.find_by_db_name(name, tenant_name)


def update(db_meta: TrinoDBMetaData):
    dal.update(db_meta)
    LOG.info(f"DBMetaData updated successfully - {db_meta}")
    return db_meta


def delete(db_meta: TrinoDBMetaData):
    dal.delete(db_meta)
    LOG.info(f"DBMetaData deleted successfully - {db_meta}")


def find_by_catalog_name(trino_catalog_name: str, tenant_name):
    return dal.find_by_catalog_name(trino_catalog_name=trino_catalog_name, tenant_name=tenant_name)
