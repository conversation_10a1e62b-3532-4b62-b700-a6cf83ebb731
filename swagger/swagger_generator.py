import marshmallow
from models.meta_data import (TrinoCatalogMetaDataSchema, TrinoDBMetaDataSchema, TrinoTableMetaDataSchema,
                              TrinoColumnMetaDataSchema)
from models.conversation_meta_data import (ConversationSchema, UserFeedbackSchema, UserQuestionSchema)

catalog_metadata_schema = TrinoCatalogMetaDataSchema()
db_metadata_schema = TrinoDBMetaDataSchema()
table_metadata_schema = TrinoTableMetaDataSchema()
column_metadata_schema = TrinoColumnMetaDataSchema()
conversation_schema = ConversationSchema()
user_feedback_schema = UserFeedbackSchema()
user_question_schema = UserQuestionSchema()

schemas = {
    'CatalogMetaData': catalog_metadata_schema,
    'DBMetaData': db_metadata_schema,
    'TableMetaData': table_metadata_schema,
    'ColumnMetaData': column_metadata_schema,
    'Conversation': conversation_schema,
    'UserFeedBack': user_feedback_schema,
    'UserQuestion': user_question_schema
}


def schema_to_swagger(schema: marshmallow.Schema, schema_name: str):
    """Converts a Marshmallow schema to Swagger schema."""
    swagger_schema = {
        'type': 'object',
        'properties': {},
    }

    for field_name, field in schema.fields.items():
        field_info = {
            'type': field.__class__.__name__.lower(),
            'description': field.metadata.get('description', ''),
        }

        if field.__class__.__name__ == 'DateTime':
            field_info['format'] = 'date-time'
        elif field.__class__.__name__ == 'String':
            if 'enum' in field.metadata:
                field_info['enum'] = field.metadata['enum']

        swagger_schema['properties'][field_name] = field_info

    return {schema_name: swagger_schema}


def generate_swagger_definitions(schemas):
    """Generates Swagger definitions from a list of schemas."""
    definitions = {}
    for schema_name, schema in schemas.items():
        definitions.update(schema_to_swagger(schema, schema_name))
    return definitions


swagger_definitions = generate_swagger_definitions(schemas)

swagger_template = {
    'swagger': '2.0',
    'info': {
        'title': 'Platform AI POC APIs',
        'description': 'API Documentation',
        'version': '1.0.0'
    },
    "host": "localhost:3000",
    'basePath': '/',
    'schemes': ['http', 'https'],
    'parameters': {
        'UserIdHeader': {
            'name': 'User-Id',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The ID of the user making the request.'
        },
        'TenantNameHeader': {
            'name': 'Tenant-Name',
            'in': 'header',
            'type': 'string',
            'required': True,
            'description': 'The name of the tenant making the request.'
        }
    },
    'definitions': swagger_definitions
}
