import json
from sqlalchemy import tuple_
import pandas as pd
from langchain_openai import OpenAIEmbeddings

from flask_app import app
from models.conversation_meta_data import db, FewShotExample
from utils.LogUtils import LOG

embedding_model = OpenAIEmbeddings(model="text-embedding-3-small")


def load_examples_to_db(csv_path: str):
    existing_records = {
        (record.tenant_name, record.question): record
        for record in db.session.query(FewShotExample).all()
    }
    LOG.info(f"Loaded {len(existing_records)} existing records from the database.")

    df = pd.read_csv(csv_path)
    LOG.info(f"Loaded {len(df)} records from CSV.")

    records_in_csv = set((row['TENANT_NAME'], row['QUESTION']) for _, row in df.iterrows())
    records_to_delete = set(existing_records.keys()) - records_in_csv
    LOG.info(f"Found {len(records_to_delete)} records to delete from the database.")

    added_count = 0
    updated_count = 0
    deleted_count = 0

    for _, row in df.iterrows():
        tenant_name = row['TENANT_NAME']
        question = row['QUESTION']
        current_key = (tenant_name, question)

        existing_example = existing_records.get(current_key)

        if existing_example:
            if existing_example.expected_trino_query != row['EXPECTED_TRINO_QUERY'] or existing_example.explanation != row['EXPLANATION']:
                LOG.info(f"Updating existing entry for tenant '{tenant_name}' and question '{question}'")
                existing_example.expected_trino_query = row['EXPECTED_TRINO_QUERY']
                existing_example.explanation = row['EXPLANATION']
                updated_count += 1
                db.session.add(existing_example)
            continue

        few_shot_example = FewShotExample(
            tenant_name=row['TENANT_NAME'],
            question=row['QUESTION'],
            expected_trino_query=row['EXPECTED_TRINO_QUERY'],
            explanation=row['EXPLANATION']
        )

        content_for_embedding = {"tenant": few_shot_example.tenant_name, "question": few_shot_example.question}

        LOG.info(f"Start creating embeddings for question - {content_for_embedding}")
        vector = embedding_model.embed_query(json.dumps(content_for_embedding))

        LOG.info(f"Generated embeddings of question - {content_for_embedding}")
        few_shot_example.question_embedding = vector
        db.session.add(few_shot_example)
        added_count += 1

    if records_to_delete:
        LOG.info(f"Deleting {len(records_to_delete)} records from the database that are not present in the CSV file.")
        deleted_count = db.session.query(FewShotExample).filter(
            tuple_(FewShotExample.tenant_name, FewShotExample.question).in_(records_to_delete)
        ).delete(synchronize_session=False)

    db.session.commit()
    LOG.info(f"Operation Summary: Added: {added_count}, Updated: {updated_count}, Deleted: {deleted_count}")
    print("Few-shot examples loaded into PostgreSQL successfully.")


def get_similar_examples(user_question: str, top_n: int = 3):
    user_question_embedding = embedding_model.embed_query(user_question)

    result = (db.session.query(FewShotExample)
              .order_by(FewShotExample.question_embedding.cosine_distance(user_question_embedding))
              .limit(top_n).all())
    return result


if __name__ == '__main__':
    with app.app_context():
        load_examples_to_db(csv_path="migration/few_shot_examples.csv")
        print("Data inserted successfully.")
