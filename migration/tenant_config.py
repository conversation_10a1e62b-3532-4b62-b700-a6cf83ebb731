from dataclasses import dataclass, field
from typing import List, Dict, Any

@dataclass
class Tenant:
    tenant_name: str
    tenant_id: str
    exclude_catalogs: List[str] = field(default_factory=list)
    exclude_databases: List[str] = field(default_factory=list)
    exclude_tables: List[str] = field(default_factory=list)

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> "Tenant":
        """Validate and create a Tenant instance from a dictionary."""
        if not data.get("tenant_name") or not data.get("tenant_id") :
            raise ValueError("Both tenant_name and tenant_id are required")

        return Tenant(
            tenant_name=data["tenant_name"],
            tenant_id=data["tenant_id"],
            exclude_catalogs=data.get("exclude_catalogs", []),
            exclude_databases=data.get("exclude_databases", []),
            exclude_tables=data.get("exclude_tables", [])
        )

@dataclass
class TenantConfig:
    tenants: List[Tenant]

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> "TenantConfig":
        """Validate and create a TenantConfig instance from a dictionary."""
        if "tenants" not in data or not isinstance(data["tenants"], list) or not data["tenants"]:
            raise ValueError("The 'tenants' field must be a non-empty list.")

        tenants = [Tenant.from_dict(tenant) for tenant in data["tenants"]]
        return TenantConfig(tenants=tenants)
