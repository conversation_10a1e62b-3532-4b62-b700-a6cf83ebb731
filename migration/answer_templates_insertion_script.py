import json
from sqlalchemy import tuple_
import pandas as pd
from langchain_openai import OpenAIEmbeddings

from flask_app import app
from models.conversation_meta_data import db, AnswerTemplate
from utils.LogUtils import LOG

embedding_model = OpenAIEmbeddings(model="text-embedding-3-small")


def load_examples_to_db(csv_path: str):
    existing_records = {
        (record.intent_classification, record.feature, record.question_template): record
        for record in db.session.query(AnswerTemplate).all()
    }
    LOG.info(f"Loaded {len(existing_records)} existing records from the database.")

    df = pd.read_csv(csv_path)
    LOG.info(f"Loaded {len(df)} records from CSV.")

    records_in_csv = set((row['INTENT_CLASSIFICATION'], row['FEATURE'], row['QUESTION_TEMPLATE']) for _, row in df.iterrows())
    records_to_delete = set(existing_records.keys()) - records_in_csv
    LOG.info(f"Found {len(records_to_delete)} records to delete from the database.")

    added_count = 0
    updated_count = 0
    deleted_count = 0

    for _, row in df.iterrows():
        intent_classification = row['INTENT_CLASSIFICATION']
        feature = row['FEATURE']
        question_template = row['QUESTION_TEMPLATE']
        current_key = (intent_classification, feature, question_template)

        existing_example = existing_records.get(current_key)

        if existing_example:
            if existing_example.response_template != row['RESPONSE_TEMPLATE'] or existing_example.explanation != row['EXPLANATION']:
                LOG.info(f"Updating existing entry for intent_classification '{intent_classification}', feature '{feature}' and question_template '{question_template}'")
                existing_example.response_template = row['RESPONSE_TEMPLATE']
                existing_example.explanation = row['EXPLANATION']
                updated_count += 1
                db.session.add(existing_example)
            continue

        few_shot_template = AnswerTemplate(
            intent_classification=row['INTENT_CLASSIFICATION'],
            feature=row['FEATURE'],
            question_template=row['QUESTION_TEMPLATE'],
            response_template=row['RESPONSE_TEMPLATE'],
            explanation=row['EXPLANATION']
        )

        content_for_embedding = {"intent_classification": few_shot_template.intent_classification, "feature": few_shot_template.feature,
                                 "question_template": few_shot_template.question_template}

        LOG.info(f"Start creating embeddings for question_template - {content_for_embedding}")
        vector = embedding_model.embed_query(json.dumps(content_for_embedding))

        LOG.info(f"Generated embeddings of question_template - {content_for_embedding}")
        few_shot_template.question_template_embedding = vector
        db.session.add(few_shot_template)
        added_count += 1

    if records_to_delete:
        LOG.info(f"Deleting {len(records_to_delete)} records from the database that are not present in the CSV file.")
        deleted_count = db.session.query(AnswerTemplate).filter(
            tuple_(AnswerTemplate.intent_classification, AnswerTemplate.feature, AnswerTemplate.question_template).in_(records_to_delete)
        ).delete(synchronize_session=False)

    db.session.commit()
    LOG.info(f"Operation Summary: Added: {added_count}, Updated: {updated_count}, Deleted: {deleted_count}")
    print("Few-shot templates loaded into PostgreSQL successfully.")


if __name__ == '__main__':
    with app.app_context():
        load_examples_to_db(csv_path="migration/answer_templates.csv")
        print("Data inserted successfully.")
