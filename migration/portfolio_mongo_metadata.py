metadata={
    "catalogs": [
      {
        "name": "marine_live_data",
        "connector_type": "MONGO",
        "description":"""
        CATALOG/DB containing vessel and policy data
        If user asks questions related to portfolio, first get the vessels 
        from respective database/tables and then only go further. Don't use json operators while building query in this MONGO catalog
        When providing the name and IMO number of a vessel, you should always format it like this in response:
        Vessel Name | IMO Number
        For example:
        CMA CGM Alexander <PERSON> | IMO 9454448
        """,

        "databases": [
            {
            "name": "questmarine_vesseldata_{TENANT_NAME}",
            "description": "Contains data for vessels and policies.",
            "tables": [
              {
                "name": "vessel_summary",
                "description": "Contains data for the vessels.",
                "columns": [
                  {"name": "_id", "type": "text",
                   "description": "This is Vessel IMO. When providing the name and IMO number of a vessel, you should always format it like this in response: Vessel Name | IMO Number. For example: CMA CGM <PERSON> | IMO 9454448."},
                  {"name": "vesselName", "type": "text",
                   "description": "Vessel Name. When providing the name and IMO number of a vessel, you should always format it like this in response: Vessel Name | IMO Number. For example: CMA CGM Alexander <PERSON> | IMO 9454448."},
                  {"name": "vesselType.typeName", "type": "text", "description": "Vessel Type"},
                  {"name": "assetStatus", "type": "enum", "description": """
                              Gives information about coverage of vessel by policy
                              Possible values - OnCover, OffCover, Unknown
                              OnCover - vessel is part of 1/more active policy
                              OffCover - vessel is part of 1/more inactive policies
                              Unknown - vessel isn't part of any policy
                              In the response, always refer to 'OnCover' as 'on cover' and 'OffCover' as 'off cover'

                              All vessels in portfolio - implies that user is asking for both of his OnCover and OffCover Vessels
                              """},
                  {"name": "vesselOwner", "type": "text",
                   "description": "Name of Company or entity that owns the vessel currently"},
                  {"name": "grossTonnage", "type": "text",
                   "description": "Gross Tonnage of vessel. In response it should be abbreviated to 'GT' while giving value for a specific vessel, like for example ‘123,020 GT’ and don't use the full form 'Gross Tonnage'."},
                  {"name": "deadweight", "type": "text", "description": "Dead Weight of vessel"},
                  {"name": "vesselStatus", "type": "text", "description": "Operational status of vessel"}

                ]
              },
              {
                "name": "insurance_policy",
                "description": "Contains data for the policies. A policy can insure multiple vessels.",
                "columns": [
 {"name": "insuredAssets", "type": "array<object>", "description": """
                This is an Array field which has information about a list of assets(vessels) and its attributes. 
                The size of this array is the number of vessels associated with that respective account.
                Since this is nested json ie array<json>, use trino json operators for fetching relevant data

                Eg - 
                "insuredAssets" : [
		            {
                        "assetAttributeInfo" : {
                            "deductible" : "123213",
                            "deductiblecurrencycode" : "USD",
                            "hullsuminsured" : "123213",
                            "hullsuminsuredcurrencycode" : "USD",
                            "vesselpremium" : "123233",
                            "vesselpremiumcurrencycode" : "USD",
                            "totalsuminsured" : "123213.00",
                            "deductibleinusercurrency" : "123213",
                            "deductibleusercurrencycode" : "USD",
                            "vesselpremiumusercurrencycode" : "USD",
                            "vesselpremiuminusercurrency" : "123233",
                            "hullsuminsuredinusercurrency" : "123213",
                            "hullsuminsuredusercurrencycode" : "USD"
			        },
                    "assetId" : VESSEL IMO -> text,
                    "assetType" : Vessel Type -> text,
                    "assetName" : Vessel Name -> text,
                    "coverFromDate" : Timestamp when policy starts covering this imo -> timestamp,
                    "coverToDate" : Timestamp when policy stops covering this imp -> timestamp,
                    "isActive" : true,
                    "assetStatus" : enum -> Possible values OnCover, OffCover and Unknown. In the response, always refer to 'OnCover' as 'on cover' and 'OffCover' as 'off cover'
		            },
		            ...more assets
		            ]
             """},
            {"name": "policyAttributeInfo", "type": "json", "description": """
                This is a JSON object field which has data related to policy and policy owner. 
                It has multiple policy related attributes.
                    1. policyNumber - text - This is the name of the policy. Always refer this name to represent a policy and give in response.
                    2. accountowner - uuid - Id of the user who owns the account
                    3. policyStatus - text - Enum indicating status of Policy
                                    Possible values {
                                        ACTIVE - policy is currently active or in-force,
                                        EXPIRED - policy start/end dates are in past
                                        DRAFT - policy is incomplete
                                        QUOTED - policy is quoted but not written
                                        WRITTEN - policy is in future
                                        CANCELLED_BY_CLIENT/CANCELLED_BY_INSURER - policy is cancelled   
                                    }
                    ** For computing live coverage - only ACTIVE POLICIES should be considered

             """},
            {"name": "policyEndDate", "type": "timestamp",
             "description": "Timestamp when policy coverage ends"},
            {"name": "policyStartDate", "type": "timestamp",
             "description": "Timestamp when policy coverage starts"},
            {"name": "policyRenewed", "type": "boolean",
             "description": "This boolean field has information if policy is renewed or not. When value = true, it implies the policy has been renewed. else its not renewed or new"},
            {"name": "counterPartyId", "type": "text",
             "description": "This is the accountId/counterpartyId of the account containing policy. Maps to table -> counterparty and field -> _id "},
            {"name": "productId", "type": "text", "description": "This is the product ID of the policy."},
            {"name": "productLineId", "type": "text",
             "description": "This is the product line ID of the policy. A product line ID can have multiple product IDs"},
            {"name": "policyCancellationDate", "type": "timestamp",
             "description": "Timestamp at which policy gets cancelled. "},
            {"name": "_id", "type": "Mongo ObjectId", "description": "Unique id assigned to policy. Always CAST this ID to VARCHAR while building Trino Query. ** Never disclose this id in the response **"}


                ]
              },
              {
                "name": "counterparty",
                "description": "Contains data for account/counterparty. An account/counterparty holds multiple policies and multiple vessels are covered in a policy. Use this table to fetch account details first and then answer questions related to policies/vessels/events for the account.",
                "columns": [
                  {"name": "_id", "type": "Mongo ObjectId",
                   "description": "Unique id assigned to account. Always CAST this ID to VARCHAR while building Trino Query.  ** Never disclose this id in the response **"},
                  {"name": "name", "type": "text", "description": "This is the name of account"},
                  {"name": "accountStatus", "type": "enum",
                   "description": """
                                      Enum representing status of account. A account contains multiple policies. So this status reflects the nature of policies in account
                                      Possible values are 
                                      1. ACTIVE : If one or more ACTIVE Policies are present
                                      2. PREVIOUS : If one or more EXPIRED, CANCELLED_BY_CLIENT or CANCELLED_BY_INSURER policies are present
                                      3. PROSPECTIVE : If only QUOTED, DRAFT or NOT_WRITTEN policies are present, this is also default status for new Account
                                      """
                   },
                  {"name": "activePolicyCount", "type": "int",
                   "description": "Number of active policies in this account"},
                  {"name": "vesselTypeCount", "type": "json",
                   "description": """
                                  Distribution of vessels by vesselType
                                  Eg 
                                  vesselTypeCount" : {
                  		            "Passenger/Ro-Ro Cargo Ship" : 1,
                  		            "Ro-Ro Cargo Ship" : 2,
                  		            "General Cargo Ship" : 1,
                  		            "Passenger (Cruise) Ship" : 1,
                  		            "Container Ship" : 1
                  	            }
                               """

                   }

                ]
              },
              {
                "name": "vessel_policy_summary",
                "description": """
                 Denormalized data of the policy assets. Since policy object is nested, running computations on it can be time-consuming
                 and difficult.
                 This table contains policy assets in denormalized format.
                 Eg - If we have a policy with 5 assets/vessels, then this table will contain 5 rows, 1 for each vessel
                 This table is used to get totalSumInsured or totalExposure and netSumInsured or netExposure
                 And its simple to run aggregations on it
            """,
                "columns": [
                  {
                    "name": "imo",
                    "type": "text",
                    "description": "This is vessel IMO"
                  },
                  {
                    "name": "vesselType",
                    "type": "text",
                    "description": "Type / category of vessel. Should not be blank or null"
                  },
                  {
                    "name": "insurancePolicyId",
                    "type": "text",
                    "description": "This is the unique Id of insurance policy"
                  },
                  {
                    "name": "counterPartyId",
                    "type": "text",
                    "description": "This is the unique Id of counterparty or account"
                  },
                  {
                    "name": "totalSumInsured",
                    "type": "float",
                    "description": "Total sum insured for a vessel under this policy. Value is USD"
                  },
                  {
                    "name": "netExposure",
                    "type": "float",
                    "description": "Net exposure for a vessel under this policy. Value is USD"
                  }
                ]
              }
            ]
          },
            {
                    "name": "questmarine_analyticsdata_{TENANT_NAME}",
                    "description": "Contains data for policy claims for vessels.",
                    "tables": [
                        {
                            "name": "claim_details",
                            "description": "Contains data related to claims on vessels.",
                            "columns": [
                                {
                                    "name": "_id",
                                    "type": "Mongo ObjectId",
                                    "description": "Unique identifier for the policy claim record. **Never disclose this ID in responses.**"
                                },
                                {
                                    "name": "clientClaimID",
                                    "type": "text",
                                    "description": "Unique identifier assigned to the claim by the client."
                                },
                                {
                                    "name": "vesselImo",
                                    "type": "text",
                                    "description": "International Maritime Organization (IMO) number uniquely identifying the vessel involved in the claim."
                                },
                                {
                                    "name": "vesselTypeName",
                                    "type": "text",
                                    "description": "Type of the vessel (e.g., Bulk Carrier) involved in the claim."
                                },
                                {
                                    "name": "vesselName",
                                    "type": "text",
                                    "description": "Name of the vessel associated with the claim."
                                },
                                {
                                    "name": "total_incurred",
                                    "type": "text",
                                    "description": "Total amount incurred for the claim, representing the overall financial value of the claim."
                                },
                                {
                                    "name": "totalIncurredCurrencyCode",
                                    "type": "text",
                                    "description": "Currency code for the total incurred amount (e.g., USD)."
                                },
                                {
                                    "name": "policy_id",
                                    "type": "text",
                                    "description": "Identifier for the insurance policy associated with the claim."
                                },
                                {
                                    "name": "counterPartyName",
                                    "type": "text",
                                    "description": "Name of the account associated with the claim."
                                },
                                {
                                    "name": "counterPartyId",
                                    "type": "text",
                                    "description": "Unique ID for the account associated with the claim."
                                },
                                {
                                    "name": "claim_status",
                                    "type": "text",
                                    "description": "Status of the claim (e.g., Open, Closed)."
                                },
                                {
                                    "name": "claim_type",
                                    "type": "text",
                                    "description": "Type of claim (e.g., Hull Damage)."
                                },
                                {
                                    "name": "claimDescription",
                                    "type": "text",
                                    "description": "Description or details about the claim."
                                },
                                {
                                    "name": "loss_date",
                                    "type": "timestamp",
                                    "description": """
                                        Date of the loss incident leading to the claim, stored as a Unix timestamp in milliseconds.
                                        This should be treated as the claim filing date.
                                    """
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}
