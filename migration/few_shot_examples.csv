TENANT_NAME,QUESTION,EXPECTED_TRINO_QUERY,EXPLANATION
stage,give me top 5 vessels in my portfolio,"SELECT_id AS vessel_imo, vesselName
FROM marine_live_data.questmarine_vesseldata_stage.vessel_summary
WHERE assetStatus IN ('OnCover', 'OffCover')
LIMIT 5;","since user asked question on portfolio, we'll consider only Oncover and Offcover vessels ."
stage,give me 5 oncover vessels,"SELECT vesselName
FROM marine_live_data.questmarine_vesseldata_stage.vessel_summary
WHERE assetStatus = 'OnCover'
LIMIT 5;",user asked about the oncover vessels so we will consider only that vessels  which   have an asset status of 'OnCover' from the vessel_summary table.
stage,Give me 5 Offcover vessels with vesselName,"SELECT _id AS vesselIMO, vesselName
FROM marine_live_data.questmarine_vesseldata_stage.vessel_summary
WHERE assetStatus = 'OffCover'
LIMIT 5;","Since user asked for OffCover vessels, we have added assetStatus = 'OffCover' condition"
stage,Show me top 5 Vessels who have generated most events in last 48 hours,"SELECT associated_entity_id, COUNT(*) AS event_count
FROM marine_events_data.""enhanced-timeline"".events_raw_revised
WHERE event_date >= current_timestamp - INTERVAL '48' HOUR
GROUP BY associated_entity_id
ORDER BY event_count DESC
LIMIT 5;","associated_entity_id is vesselImo. So we are getting vesselImo and count of events grouped by imo and listing top 5 imos
with most events in last 48 hrs"
stage,Show me top 10 Vessels who have generated most events in last 24 hours,"SELECT associated_entity_id, COUNT(*) AS event_count
FROM marine_events_data.""enhanced-timeline"".events_raw_revised
WHERE event_date >= current_timestamp - INTERVAL '24' HOUR
GROUP BY associated_entity_id
ORDER BY event_count DESC
LIMIT 10;","associated_entity_id is vesselImo. So we are getting vesselImo and count of events grouped by imo and listing top 10 imos(because limit is 10)
with most events in last 24 hrs"
stage," include IMO, Vessel Name and Vessel Type in top 5 Vessels who have generated most events in last 48 hours","SELECT
    e.associated_entity_id AS vesselIMO,
    e.vessel_name AS vesselName,
    e.vessel_type AS vesselType,
    COUNT(*) AS event_count
FROM
    marine_events_data.""""enhanced-timeline"""".events_raw_revised e
WHERE
    e.event_date >= current_timestamp - INTERVAL '48' HOUR
GROUP BY
    e.associated_entity_id,
    e.vessel_name,
    e.vessel_type
ORDER BY
    event_count DESC
LIMIT 5;
","When user asks for vessel name, imo and type of vessels who have generated most events in last 48 hours, add vessel_name and vessel_type from marine events data only
He has not asked for vessels of portfolio"
stage,Show me top 5 Vessels of my portfolio who have generated most events in last 48 hours,"SELECT
    e.associated_entity_id AS vesselIMO,
    e.vessel_name AS vesselName,
    v.vesselType.typeName AS vesselType,
    COUNT(*) AS event_count
FROM
    marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN
    marine_live_data.questmarine_vesseldata_stage.vessel_summary v
    ON e.associated_entity_id = v._id
WHERE
    e.event_date >= current_timestamp - INTERVAL '48' HOUR
    AND v.assetStatus IN ('OnCover', 'OffCover')
GROUP BY
    e.associated_entity_id,
    e.vessel_name,
    v.vesselType.typeName
ORDER BY
    event_count DESC
LIMIT 5;","Since the user asked for vessels in my portfolio - we need to join events data with vessel summary data in mongo
and fetch only vessels where assetStatus = OnCover or OffCover"
stage,Now show me last 10 events generated by my vessels,"SELECT
    e.associated_entity_id AS vesselIMO,
    e.vessel_name AS vesselName,
    e.event_type AS eventType,
    e.event_date AS eventTimestamp
FROM
    marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN
    marine_live_data.questmarine_vesseldata_stage.vessel_summary v
    ON e.associated_entity_id = v._id
WHERE
    v.assetStatus IN ('OnCover', 'OffCover')
ORDER BY
    e.event_date DESC
LIMIT 10;","Since user asked about the last 10  events in my vessel so we will consider only last 10 events(because limit is 10) for  the vessel with ""onCover "" and ""offcover "" assetStatus by joining marine events data with marine live data . "
stage,Show me top 20 Vessels of my portfolio who have generated most events in last 48 hours,"SELECT
    e.associated_entity_id AS vesselIMO,
    e.vessel_name AS vesselName,
    v.vesselType.typeName AS vesselType,
    COUNT(*) AS event_count
FROM
    marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN
    marine_live_data.questmarine_vesseldata_stage.vessel_summary v
    ON e.associated_entity_id = v._id
WHERE
    e.event_date >= current_timestamp - INTERVAL '48' HOUR
    AND v.assetStatus IN ('OnCover', 'OffCover')
GROUP BY
    e.associated_entity_id,
    e.vessel_name,
    v.vesselType.typeName
ORDER BY
    event_count DESC
LIMIT 20;","Since the user asked for top 20 vessels in my portfolio - we need to join events data with vessel summary data in mongo
and fetch only 20 vessels where assetStatus = OnCover or OffCover"
stage,is there a correlation between vessel type and event frequency and volume?,"SELECT
    v.vesselType.typeName AS vesselType,
    COUNT(DISTINCT e.associated_entity_id) AS vessel_count
FROM
    marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN
    marine_live_data.questmarine_vesseldata_stage.vessel_summary v
    ON e.associated_entity_id = v._id
GROUP BY
    v.vesselType.typeName
ORDER BY
    vessel_count DESC;","if question asked about correlation between vessel type and event frequency you should count the number of distinct vessels for each type, grouping by vessel type (vesselType) and ordering the results by the count in descending order. The query joins the events_raw_revised and vessel_summary tables on the vessel IMO number."

stage,"How many owenership changes has this veseel ""5261922"" had ?","SELECT COUNT(*) AS ownership_changes FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '5261922' AND event_type = 'REGISTERED_OWNER'","when user ask for ownership chnages you have to count  the number of 'REGISTERED_OWNER' events for a specific vessel, identified by the IMO number mentioned in the question ', from the events_raw_revised table."
stage,Can you show me ownership changes for vessel - 5261922,"SELECT json_extract(event_data, '$.previous_value') AS previous_owner, json_extract(event_data, '$.current_value') AS current_owner, json_extract(event_data, '$.current_change_date') AS change_date FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '5261922' AND event_type = 'REGISTERED_OWNER'","when user asks for the details of ownership changes you have to extract and retrieve the previous owner, current owner, and change date from the event_data JSON field for 'REGISTERED_OWNER' events associated with the vessel identified by '5261922'."
stage,Provide summary narrative of what happened with this vessel 9976147 in last 12 months,"SELECT event_type,event_category, event_data, event_date FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9976147'AND event_date >= date_add('year', -1, current_date) ORDER BY event_date DESC","for question related to summary you have to provide  all the details  including the event type,  data, and timestamp, ordering the results by timestamp in descending order and give the result in narrative format. Dont give event_category in response."
stage,When did Maersk buy this vessel 9164237,"SELECT json_extract_scalar(event_data, '$.current_value') AS buyer, event_date AS purchase_date FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9164237' AND event_type = 'REGISTERED_OWNER' ORDER BY event_date LIMIT 1","When user asks for a particular owner when he bought a vessel you should retrieve the most recent purchase date and buyer information for the vessel identified by '9164237', where the event type is 'REGISTERED_OWNER'. It extracts the buyer from the event_data JSON field and orders the results by timestamp, limiting to the most recent entry."
stage,Who was the primary technical manager for 9164237 in 2015,"SELECT json_extract_scalar(event_data, '$.current_value') AS technical_manager FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9164237' AND event_type = 'TECHNICAL_MANAGER' AND year(event_date) = 2015 ORDER BY event_date LIMIT 1","when user asks for primary technical manager for a vessel, for example vessel ""9164237"", you should retrieve the technical manager for the vessel identified by '9164237' from the year 2015. It extracts the technical manager from the event_data JSON field and orders the results by timestamp, limiting to the most recent entry for that year."
stage,Who was technical manager before for vessel  9164237 before?,"SELECT json_extract_scalar(event_data, '$.previous_value') AS previous_technical_manager FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9164237' AND event_type = 'TECHNICAL_MANAGER' AND year(event_date) = 2015 ORDER BY event_date LIMIT 1","when user asks for previous technical manager for vessel, you should retrieve the most recent previous technical manager for the vessel identified by '9164237' from the year 2015. It extracts the previous technical manager from the event_data JSON field, ordering by timestamp and limiting to the most recent entry for that year."
stage,How has the risk profile of the vessel 9164237 changed over the last 12 months,"SELECT event_type, COUNT(*) AS event_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9164237' AND event_date >= date_add('year', -1, current_date) GROUP BY event_type ORDER BY event_count DESC",When user ask for risk profile for a vessel over last 12 months you have to count the number of events of each type for the vessel identified by '9164237' that have occurred in the past year. It groups the results by event type and orders them by the event count in descending order.
stage,How has the risk profile of this vessel9164237 changed over the last 5 hours,"SELECT event_type, COUNT(*) AS event_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9164237' AND event_date >= date_add('year', -5, current_date) GROUP BY event_type ORDER BY event_count DESC",When user ask for risk profile for a vessel over last 12 months you have to count the number of events of each type for the vessel identified by '9164237' that have occurred in the last 5 years. It groups the results by event type and orders them by the event count in descending order.
stage,How has the risk profile of this vessel 9708552 changed since last 2 years,"SELECT event_type, COUNT(*) AS event_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_date >= date_add('year', -2, current_date) GROUP BY event_type ORDER BY event_count DESC",When user ask for risk profile for a vessel for example here 9708552 for over last 2 years you have to count the number of events of each type for the vessel identified by '9708552' that have occurred in the past 2 years. It groups the results by event type and orders them by event count in descending order.
stage,How has the risk profile of this vessel 9708552 changed since persian gulf incident,"SELECT MIN(event_date) AS incident_date FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND json_extract_scalar(event_data, '$.casualty_type') = 'Persian Gulf Incident'","When user ask for risk profile for a vessel for example 9708552 after persian gulf incident, you have to retrieve the earliest date (incident_date) of events with a casualty type of 'Persian Gulf Incident' for the vessel identified by '9708552'. It extracts the casualty type from the event_data JSON field."
stage,How has the risk profile of this vessel 9708522 changed since last 2 years,"SELECT event_type, COUNT(*) AS event_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708522' AND event_date >= date_add('year', -2, current_date) GROUP BY event_type ORDER BY event_count DESC","When user ask for risk profile for a vessel, for example 9708522 over last 2 years you have to count the number of events of each type for the vessel identified by '9708522' that have occurred in the past 2 years. It groups the results by event type and orders them by the event count in descending order."
stage,How many vessels are oncover in my portfolio,SELECT COUNT(*) AS oncover_vessels FROM marine_live_data.questmarine_vesseldata_stage.vessel_summary WHERE assetStatus = 'OnCover',If user asks for number of oncover vessel in their portfolio you should count the total number of vessels with an asset status of 'OnCover' in the vessel_summary table.
stage,which policy has maximum number of vessel coverage ?,"SELECT insurancePolicyId, SUM(totalSumInsured) as totalSumInsured , SUM(netExposure) as totalNetExposure
 FROM
 marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP insurancePolicyId BY ORDER BY totalSumInsured DESC LIMIT 1",Vessel Summary contains deflated details of coverage of vessel by policy. So querying here is much easier.
stage,give me the list of accounts due to renew in next 6 months,"SELECT DISTINCT counterPartyId
FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy
WHERE policyEndDate BETWEEN CURRENT_DATE AND DATE_ADD('MONTH', 6, CURRENT_DATE)",it includes counterparty id for which policy end date is due in 6 months from current time
stage,How many active accounts in my portfolio,SELECT COUNT(*) FROM marine_live_data.questmarine_vesseldata_stage.counterparty WHERE accountStatus = 'ACTIVE';,"it give the count of vessel whose assetstatus are ""onCover"" and ""offCover"""
stage,Give me vessel count in my portfolio,"SELECT COUNT(*) AS vessel_count FROM marine_live_data.questmarine_vesseldata_stage.vessel_summary WHERE assetStatus IN ('OnCover', 'OffCover');",it should include vessel count for vessel status oncover and offcover
stage,How many accounts  in my portfolio,SELECT COUNT(*) FROM marine_live_data.questmarine_vesseldata_stage.counterparty,Simply need to get count of all accounts from counterparty table
stage,List all types of events that can happen,"SELECT DISTINCT event_type FROM marine_events_data.""enhanced-timeline"".events_raw_revised",it should list all event types from event_type table
stage,what are event categories,"SELECT DISTINCT event_category FROM marine_events_data.""enhanced-timeline"".events_raw_revised",it should fetch event_category distinct entries from events_raw_revised table
stage,give me 5 oncover vessels with their vessel type,"SELECT vesselName, vesselType.typeName FROM marine_live_data.questmarine_vesseldata_stage.vessel_summary WHERE assetStatus = 'OnCover' LIMIT 5",it should include vessel with asset status = OnCover marine live data and event type from marine events data
stage,Show me events for AGNES 83 for last 12 months,"SELECT event_type, event_category, event_date, json_extract(event_data, '$') AS event_details FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE vessel_name = 'AGNES 83' AND event_date >= current_date - interval '12' month LIMIT 10",it should include all the events of vessel name mentioned for last 12 month
stage,Did this vessel imo 9708552 had any casualties events?,"SELECT COUNT(*) AS casualty_event_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_type = 'CASUALTIES'","when user ask question like this It should check event type for particular imo .eg: in this,s event type will be casualities. "
stage,How many vessel inspections / detentions events happened for my portfolio last 6 months,"SELECT COUNT(*) AS event_count
FROM marine_events_data.""enhanced-timeline"".events_raw_revised
WHERE event_type IN ('PORT_STATE_CONTROL_DETAINED', 'PORT_STATE_CONTROL_NOT_DETAINED')
AND event_date >= current_date - interval '6' month;",For this you need to check event_type as Port state control events and count events for vessels
stage,Who was last ship manager of vessel 9708552,"SELECT json_extract_scalar(event_data, '$.current_value') AS last_ship_manager FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_type = 'SHIP_MANAGER' ORDER BY event_date DESC LIMIT 1",For this you need to check ship manager change event type for required vessel
stage,What is the ship type of vessel 9708552,SELECT vesselType.typeName FROM marine_live_data.questmarine_vesseldata_stage.vessel_summary WHERE _id = '9708552',for this you have to check the event type of vessel imo given eg: in this  case vessel type will be SHIP_TYPE then fetch data from event_data field
stage,Did this vessel imo 9708552 had name change event?,"SELECT event_date, event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_type = 'NAME_CHANGES' ORDER BY event_date DESC LIMIT 1",for this you need to check event_type name changes for the required vessel
stage,Who is the operator of vessel imo 9708552,"SELECT event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_type = 'OPERATOR' ORDER BY event_date DESC LIMIT 1",for this you need to check Operator event_type and then find details of operator in events_data field
stage,what is ship certificate status of 9708552,"SELECT event_date, event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_type = 'SHIP_CERTIFICATES' ORDER BY event_date DESC LIMIT 1",for this you need to check ship certificates change event type for required vessel
stage,Tell me recent 5 JWLA zone events,"SELECT event_date, associated_entity_id, event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE event_type = 'JWLA_ZONES' ORDER BY event_date DESC LIMIT 5",for this you have to fetch most recent 5 jwla zones
stage,Tell me top ship certificate events for 9708552,"SELECT event_date, event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_type = 'SHIP_CERTIFICATES' ORDER BY event_date DESC LIMIT 5",it should check ship certificate change event_type for required vessel
stage,Tell me 5 vessel flag changes event in 2023,"SELECT event_date, associated_entity_id, event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE event_type = 'VESSEL_FLAG' AND year(event_date) = 2023 ORDER BY event_date DESC LIMIT 5",for this you should check vessel flag change event type for the year
stage,What are vessel types that has most jwla zone events,"SELECT vessel_type, COUNT(*) AS event_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE event_type = 'JWLA_ZONES' GROUP BY vessel_type ORDER BY event_count DESC LIMIT 1",for this you need to check vessel type and count where most jwla zone event has happened
stage,Tell me recent 5 port state control events,"SELECT event_date, event_type, event_data
FROM marine_events_data.""enhanced-timeline"".events_raw_revised
WHERE event_type IN ('PORT_STATE_CONTROL_DETAINED', 'PORT_STATE_CONTROL_NOT_DETAINED')
ORDER BY event_date DESC
LIMIT 5;
",for this you have to check the event type port state control which occured recently
stage,Tell me ship status change event of 9708552 if any,"SELECT event_date, event_type, event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = '9708552' AND event_type = 'SHIP_STATUS'",it should check event type ship status change for the required vessel
stage,Which vessel had most Ship manager change event,"SELECT associated_entity_id, COUNT(*) AS ship_manager_change_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE event_type = 'SHIP_MANAGER' GROUP BY associated_entity_id ORDER BY ship_manager_change_count DESC LIMIT 1",it should check event ship manager  and give the count
stage,How many active policies I have got in my portfolio?,SELECT COUNT(*) AS active_policies_count FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy WHERE policyAttributeInfo.policyStatus='ACTIVE',"it should give all policies the in portfolio which have policy status=""Active"""
stage,Can you give me summary of policies in my portfolio,"SELECT policyAttributeInfo.policyStatus AS policy_status, COUNT(*) AS policy_count
FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy
GROUP BY policyAttributeInfo.policyStatus
LIMIT 20",it should give summary of data of policies in insurance policy table
stage,How many inactive policies I have got in my portfolio ?,SELECT COUNT(*) AS active_policies_count FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy WHERE policyAttributeInfo.policyStatus !='ACTIVE',"it should give all policies the in portfolio which are not active means policy status =""!=ACTIVE"""
dev,Which vessels (identified by IMO) have active policies?,SELECT COUNT(1) from marine_live_data.questmarine_vesseldata_dev.vessel_summary where assetStatus = 'OnCover';,it should list all the vessels which are oncover asset status
dev,Which accounts have the most policies associated with them?,"SELECT
CAST(c._id AS VARCHAR) AS account_id,
c.name as account_name,
COUNT(1) as policy_count
from marine_live_data.questmarine_vesseldata_dev.counterparty c
INNER JOIN marine_live_data.questmarine_vesseldata_dev.insurance_policy p
ON p.counterPartyId = CAST(c._id AS VARCHAR)
GROUP BY 1, 2
ORDER BY 3 DESC
LIMIT 10;
",We join policies and accounts and check the count based on account
stage,Which product lines or products are most commonly associated with vessel policies?,"SELECT productLineId, productId, COUNT(_id) AS policy_count
FROM marine_live_data.questmarine_vesseldata_dev.insurance_policy
GROUP BY 1,2
ORDER BY policy_count DESC LIMIT 10;",Grouping data of insurance policy by productLine and product
stage,Which entities are most often associated with events?,"SELECT associated_entity_type, associated_entity_id, COUNT(*) AS event_count
   FROM ""enhanced-timeline"".events_raw_revised
   GROUP BY associated_entity_type, associated_entity_id
   ORDER BY event_count DESC LIMIT 10;",grouping entries of events table by entityType and entityId
stage,Which tags are most commonly associated with past events?,"SELECT tags, COUNT(*) AS tag_count
   FROM ""enhanced-timeline"".events_raw_revised
   GROUP BY tags
   ORDER BY tag_count DESC LIMIT 10;",grouping entries of events table by tags
stage,who is current owner of vessel - 9999993,"SELECT _id, vesselName, vesselOwner from marine_live_data.questmarine_vesseldata_stage.vessel_summary where _id = '9999993';",The current vesselOwner is present in vessel Summary table.
stage,look at vessels in my portfolio and tell me what vessel owners are contributing to most of the events,"SELECT
    v.vesselOwner, COUNT(1) as eventCount
    FROM
    marine_events_data.""enhanced-timeline"".events_raw_revised e
    JOIN marine_live_data.questmarine_vesseldata_stage.vessel_summary v
    ON e.associated_entity_id = v._id
    WHERE v.assetStatus IN ('OnCover', 'OffCover')
    GROUP BY v.vesselOwner
    ORDER BY eventCount DESC
    LIMIT 10","We are joining vesselSummary and events data on IMO
And grouping it based on vesselSummary.vesselOwner"
stage,Who is current owner of vessel - ARCHON,"SELECT _id, vesselName, vesselOwner from marine_live_data.questmarine_vesseldata_stage.vessel_summary where LOWER(vesselName) LIKE LOWER('ARCHON');",Looking for currentOwner in vessel Summary table. The `LOWER` operator is used because vessel names can contain special characters allowing for a case-insensitive comparison while preserving the special characters in them.
stage,Which vessel owner owns most of the vessels in my portfolio,"SELECT vesselOwner, COUNT(_id) AS ownedVesselCount
from marine_live_data.questmarine_vesseldata_stage.vessel_summary
WHERE assetStatus IN ('OnCover', 'OffCover')
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10","We are looking at current vessels in portfolio (OnCover and OffCover)
and grouping them by VesselOwner"
stage,What is tonnage and deadweight for vessel 9999993,"SELECT _id, vesselName, grossTonnage, deadweight
from marine_live_data.questmarine_vesseldata_stage.vessel_summary
WHERE _id = '9999993'",Getting grossTonnage and deadweight.
stage,What is tonnage and deadweight for vessel PACIFIC JAYA XIX,"SELECT _id, vesselName, grossTonnage, deadweight
from marine_live_data.questmarine_vesseldata_stage.vessel_summary
WHERE LOWER(vesselName) LIKE LOWER('PACIFIC JAYA XIX')",Getting grossTonnage and deadweight.The `LOWER` operator is used because vessel names can contain special characters allowing for a case-insensitive comparison while preserving the special characters in them.
stage,Which vesselTypes have most tonnage,"SELECT vesselType.typeName, SUM(grossTonnage) as totalGrossTonnage
from marine_live_data.questmarine_vesseldata_stage.vessel_summary
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10","Since the question is generic, we consider all the vessels"
stage,Which vesselTypes in my portfolio have most tonnage,"SELECT vesselType.typeName, SUM(grossTonnage) as totalGrossTonnage
from marine_live_data.questmarine_vesseldata_stage.vessel_summary
WHERE assetStatus IN ('OnCover', 'OffCover')
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10",Since the question is about vessels in portfolio - we consider oncover and offcover
stage,Which vessels have maximum exposure in my portfolio,"SELECT imo, SUM(totalSumInsured) AS sum_totalSumInsured, SUM(netExposure) as sum_netExposure
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10;",vessel_policy_summary only conatins the insured vessels
stage,Which vesselTypes have maximum exposure in my portfolio,"SELECT vesselType, SUM(totalSumInsured) AS sum_totalSumInsured, SUM(netExposure) as sum_netExposure
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10;",aggregating totalSumInsured and netExposure in vessel_policy_summary by vesselType
stage,Which policies have maximum exposure in my portfolio,"SELECT insurancePolicyId, SUM(totalSumInsured) AS sum_totalSumInsured, SUM(netExposure) as sum_netExposure
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10;",aggregating totalSumInsured and netExposure in vessel_policy_summary by policy
stage,Which accounts have maximum exposure in my portfolio,"SELECT counterPartyName, SUM(totalSumInsured) AS sum_totalSumInsured, SUM(netExposure) as sum_netExposure
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP BY 1,2
ORDER BY 3 DESC
LIMIT 10;",aggregating totalSumInsured and netExposure in vessel_policy_summary by account
stage,Which products have maximum exposure in my portfolio,"SELECT productId, SUM(totalSumInsured) AS sum_totalSumInsured, SUM(netExposure) as sum_netExposure
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10;",aggregating totalSumInsured and netExposure in vessel_policy_summary by product
stage,Which productLines have maximum exposure in my portfolio,"SELECT productLineId, SUM(totalSumInsured) AS sum_totalSumInsured, SUM(netExposure) as sum_netExposure
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP BY 1
ORDER BY 2 DESC
LIMIT 10;",aggregating totalSumInsured and netExposure in vessel_policy_summary by productLine
stage,Provide summary of what happened with this vessel 9999993 since last renewal,"SELECT e.event_type, e.event_category, e.event_data, e.event_date
FROM marine_events_data.'enhanced-timeline'.events_raw_revised e
JOIN marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    ON e.associated_entity_id = ANY (
        SELECT insuredAsset.assetId
        FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip,
             UNNEST(ip.insuredAssets) AS insuredAsset
        WHERE insuredAsset.assetId = '9999993'
    )
WHERE e.event_date > (
    SELECT MAX(policyStartDate)
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip,
         UNNEST(ip.insuredAssets) AS insuredAsset
    WHERE insuredAsset.assetId = '9999993' AND ip.policyAttributeInfo.policyType = 'Renewal' AND ip.policyAttributeInfo.policyStatus IN ('ACTIVE', 'EXPIRED','WRITTEN')
) AND associated_entity_id='9999993'
ORDER BY e.event_date DESC
LIMIT 10;","for question related to summary since the last renewal you have to check the policyType='Renewal' for particular vessel and give the policyStartDate for that and after that events will be calculated for that vessel"
stage,Provide a summary of what’s happened with 'MSC Mediterranean Shipping Co' account since the last renewal,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('MSC Mediterranean Shipping Co')
),
LatestRenewalDate AS (
    SELECT MAX(ip.policyStartDate) AS max_renewal_date
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    JOIN CounterpartyInfo cpi
        ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
),
VesselAssets AS (
    SELECT DISTINCT insuredAsset.assetId
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset
    JOIN CounterpartyInfo cpi
        ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
    WHERE ip.policyAttributeInfo.policyType = 'Renewal'
      AND ip.policyAttributeInfo.policyStatus IN ('ACTIVE', 'EXPIRED', 'WRITTEN')
      AND ip.policyStartDate = (SELECT max_renewal_date FROM LatestRenewalDate)
),
FilteredEvents AS (
    SELECT e.associated_entity_id, e.event_type, e.event_category, e.event_data, e.event_date
    FROM marine_events_data.'enhanced-timeline'.events_raw_revised e
    JOIN VesselAssets va
        ON e.associated_entity_id = va.assetId
    WHERE e.event_date > (SELECT max_renewal_date FROM LatestRenewalDate)
)
SELECT *
FROM FilteredEvents
ORDER BY event_date DESC
LIMIT 10;","for this type of question we are identifying vessels in the account that fulfill the criteria of renewal and then checking events of those vessels and summarise those events. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
stage,When was the last port state control detention for 'MSC Mediterranean Shipping Co account' and how long was it detained?,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('MSC Mediterranean Shipping Co')
),
VesselAssets AS (
    SELECT DISTINCT insuredAsset.assetId
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset
    JOIN CounterpartyInfo cpi
        ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
),
PortStateControlDetentions AS (
    SELECT
        e.associated_entity_id AS vessel_asset_id,
        e.event_type,
        e.event_date,
        JSON_EXTRACT_SCALAR(e.event_data, '$.number_of_days_detained') AS detention_period
    FROM marine_events_data.'enhanced-timeline'.events_raw_revised e
    JOIN VesselAssets va
        ON e.associated_entity_id = va.assetId
    WHERE e.event_type = 'PORT_STATE_CONTROL_DETAINED'
      AND JSON_EXTRACT_SCALAR(e.event_data, '$.ship_detained') = 'true'
)
SELECT
    psc.event_type,
    psc.vessel_asset_id,
    psc.event_date,
    psc.detention_period
FROM PortStateControlDetentions psc
ORDER BY psc.event_date DESC
LIMIT 1;","for this type of question we are first identifying vessels associated with that account and then find port state control detention event happened by checking with given criteria and giving details of those events and detention period. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
stage,Who was the primary technical manager for 'MSC Mediterranean Shipping Co' account in 2015?,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('MSC Mediterranean Shipping Co')
),
VesselAssets AS (
    SELECT DISTINCT insuredAsset.assetId
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset
    JOIN CounterpartyInfo cpi
        ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
),
TechnicalManagerEvents AS (
    SELECT
        e.associated_entity_id AS vessel_asset_id,
        JSON_EXTRACT_SCALAR(e.event_data, '$.TECHNICAL_MANAGER') AS technical_manager,
        JSON_EXTRACT_SCALAR(e.event_data, '$.current_change_date') AS current_change_date
    FROM marine_events_data.""enhanced-timeline"".events_raw_revised e
    JOIN VesselAssets va
        ON e.associated_entity_id = va.assetId
    WHERE e.event_type = 'TECHNICAL_MANAGER'
      AND (
          (YEAR(CAST(JSON_EXTRACT_SCALAR(e.event_data, '$.current_change_date') AS DATE)) = 2015
           OR YEAR(CAST(JSON_EXTRACT_SCALAR(e.event_data, '$.current_change_date') AS DATE)) < 2015)
      )
),
ManagerCount AS (
    SELECT
        technical_manager,
        COUNT(DISTINCT vessel_asset_id) AS vessel_count
    FROM TechnicalManagerEvents
    GROUP BY technical_manager
)
SELECT technical_manager
FROM ManagerCount
ORDER BY vessel_count DESC
LIMIT 1;","for this type of question we are first identifying vessels associated with the given account and then find technical manager events for those vessels then identify vessels with most number of same technical manager for that time frame asked in question and give technical manager with vessel count. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
stage,give me count of vessel for the account Test,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('Test')
),
VesselAssets AS (
    SELECT DISTINCT t.assetId
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS t(assetAttributeInfo, assetId, assetType, assetName, assetStatus, coverFromDate, coverToDate, isActive)
    JOIN CounterpartyInfo cpi
        ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
)
SELECT COUNT(DISTINCT assetId) AS vessel_count
FROM VesselAssets;","for this type of question, we are first identifying the account name in counterparty and then looking for vessels associated with that account in insurance_policy and then returning distinct count of the vessels. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
stage,Give me the policy details for the account TestRisk,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('TestRisk')
)
SELECT ip._id AS policy_id, ip.policyStartDate, ip.policyEndDate, ip.policyAttributeInfo.policyStatus, ip.policyAttributeInfo.policyNumber
FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
JOIN CounterpartyInfo cpi
    ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)","for this type of question, we are first identifying the account name in counterparty and then looking for the same counterparty id in insurance policy for policy details of that account. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
stage,Give me most frequent events for fleet test1234,"SELECT er.event_type, COUNT(*) AS event_count
FROM marine_events_data.'enhanced-timeline'.events_raw_revised er
JOIN (
    SELECT CAST(vessel_id AS VARCHAR) AS vessel_id
    FROM marine_events_data.'fleet_detail'.fleet_info,
         UNNEST(vessel_ids) AS t (vessel_id)
    WHERE LOWER(fleet_name) LIKE LOWER('test1234')
    AND tenant_id = <TENANT_ID OF RESPECTIVE TENANT>
    AND status = 'ACTIVE'
) fi ON er.associated_entity_id = fi.vessel_id
GROUP BY er.event_type
ORDER BY event_count DESC
LIMIT 10;","for question related to fleet first you have to get the all vessel id which have status ACTIVE and tenant_id will be according to the tenant name. The `LOWER` operator is used because fleet names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
stage,Give me vessel count for fleet Test1234,"SELECT vessel_count FROM marine_events_data.fleet_detail.fleet_info WHERE LOWER(fleet_name) LIKE LOWER('Test1234') AND tenant_id = <TENANT_ID OF RESPECTIVE TENANT> AND status = 'ACTIVE' LIMIT 1;","for question related to vessel count give the count of vessel with the fleet name asked in question"
stage,What is my biggest account in terms of number of vessels?,"SELECT counterPartyName, COUNT(DISTINCT imo) AS vesselCount
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
GROUP BY counterPartyName
ORDER BY vesselCount DESC
LIMIT 1","for question related to biggest account in terms of vessel, it should give the account name with highest number of vessels"
stage,Summarize the list of casualties that took place for this account BulkVesselAccount in last 2 years,"SELECT
    e.associated_entity_id,
    e.vessel_name,
    e.event_date,
    e.event_data,
    COUNT(*) OVER () AS total_casualties
FROM marine_events_data.'enhanced-timeline'.events_raw_revised e
JOIN marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary vps
ON e.associated_entity_id = vps.imo
WHERE e.event_type = 'CASUALTIES'
  AND LOWER(vps.counterPartyName) LIKE LOWER('BulkVesselAccount')
  AND e.event_date >= date_add('year', -2, current_date)
ORDER BY e.event_date DESC
LIMIT 10;
","for question related to summary of particular event for a particular account"
stage,When does the policy for IMO 9147394 expire?,"SELECT insuredAsset.coverToDate
FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset
WHERE insuredAsset.assetId = '9147394' AND ip.policyAttributeInfo.policyStatus IN ('ACTIVE', 'EXPIRED')
ORDER BY insuredAsset.coverToDate DESC
LIMIT 1","for this you have to fetch the date of expiration of policy for the given vessel"
stage,What is the cover value for policy TestPolicy on the vessel IMO 1234567?,"WITH PolicyInfo AS (
    SELECT
        ip.policyAttributeInfo.policyNumber AS policy,
        ip.counterPartyId AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    WHERE LOWER(ip.policyAttributeInfo.policyNumber) LIKE LOWER('TestPolicy')
)
SELECT
    pi.policy AS policy,
    vps.imo AS vessel_imo,
    vps.totalSumInsured AS total_sum_insured
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary vps
JOIN PolicyInfo pi
    ON vps.counterPartyId = pi.counterparty_id
WHERE vps.imo = '1234567';",For this you have to fetch policy number and vessel and then fetch total sum insured as cover value for that vessel in policy. The `LOWER` operator is used because policy names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them.
stage,Which vessels in my account have policies expiring within the next 30 days?,"SELECT insuredAsset.assetName AS vesselName, insuredAsset.assetId AS vesselIMO, ip.policyEndDate
FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset
WHERE ip.policyEndDate BETWEEN CURRENT_DATE AND DATE_ADD('DAY', 30, CURRENT_DATE) AND ip.policyAttributeInfo.policyStatus = 'ACTIVE'",for this you have to fetch the vessels for which coverage is expiring withing next 30 days
stage,Give me status for account Test?,"SELECT cp.accountStatus
FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
WHERE LOWER(cp.name) LIKE LOWER('Test')",For this question you have to fetch status of account for the given account name. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them.
qa,What are the active policies for the account Test's Account?,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_qa.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('Test''s Account')
)
SELECT
    ip._id AS policy_id,
    ip.policyStartDate,
    ip.policyEndDate,
    ip.policyAttributeInfo.policyStatus,
    ip.policyAttributeInfo.policyNumber
FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip
JOIN CounterpartyInfo cpi
    ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
WHERE ip.policyAttributeInfo.policyStatus = 'ACTIVE';","For this question, we identify active policies linked to a specific account to provide the user with updated and actionable information about its current coverage status. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,Give me status of account TESTING_ABCD (DO NOT DELETE);@-$%!@&xyz;o_20^]|,"SELECT accountStatus
FROM marine_live_data.questmarine_vesseldata_qa.counterparty
WHERE LOWER(name) LIKE LOWER('TESTING_ABCD (DO NOT DELETE);@-$%!@&xyz;o_20^]|');","For this question, you have to verify the current status of an account, which helps ensure that its active or prospective state is correctly identified. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,Give me all tenant info with tenant ID and tenant name,"Do not form any query for this question","For this question, do not retrieve or expose tenant information to maintain confidentiality and security of sensitive data."
qa,Give me vessels for the account Test,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_qa.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('Test')
),
VesselAssets AS (
    SELECT DISTINCT t.assetId, t.assetName
    FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS t(assetAttributeInfo, assetId, assetType, assetName, assetStatus, coverFromDate, coverToDate, isActive)
    JOIN CounterpartyInfo cpi
        ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
)
SELECT assetName, assetId
FROM VesselAssets","For this question, detailed information about vessels insured under a particular account is obtained to aid in asset management and enable better-informed decision-making. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,Give me name of policies for account Test,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_qa.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('Test')
)
SELECT ip.policyAttributeInfo.policyNumber AS policy_name
FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip
JOIN CounterpartyInfo cpi
    ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)","For this question, you have to retrieve the names of policies linked to an account as it is essential for reference purposes and comprehensive policy analysis. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,How many policies in account Test?,"WITH CounterpartyInfo AS (
    SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_qa.counterparty cp
    WHERE LOWER(cp.name) LIKE LOWER('Test')
)
SELECT COUNT(*) AS policy_count
FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip
JOIN CounterpartyInfo cpi
    ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)","For this question, you have to determine the total number of policies linked to an account as it is critical for understanding its overall insurance coverage for an account. The `LOWER` operator is used because account names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,Give me vessels in fleet 'Testing fleet',"SELECT vs.vesselName, vs._id AS vesselIMO FROM marine_live_data.questmarine_vesseldata_qa.vessel_summary vs WHERE vs._id IN (SELECT CAST(t.vessel_id AS VARCHAR) FROM marine_events_data.""fleet_detail"".fleet_info, UNNEST(vessel_ids) AS t (vessel_id) WHERE LOWER(fleet_name) LIKE LOWER('Testing fleet') AND tenant_id = <TENANT_ID OF RESPECTIVE TENANT> AND status = 'ACTIVE')","For this question, extracting information on vessels within a specified fleet is necessary to ensure that fleet management processes are supported with accurate and current data. The `LOWER` operator is used because fleet names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,Give me policy details of policy TEST ER_ABCD}-_%`~,"SELECT
    ip.policyStartDate,
    ip.policyEndDate,
    ip.policyAttributeInfo.policyStatus,
    ip.policyAttributeInfo.policyNumber
FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip
WHERE LOWER(ip.policyAttributeInfo.policyNumber) LIKE LOWER('TEST ER_ABCD}-_%`~')","For this question, you have to obtain detailed attributes of a specific policy is essential for a complete understanding of its terms and status. The `LOWER` operator is used because policy names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,Give me vessel details of vessels in policy TEST ER_ABCD}-_%`~,"SELECT DISTINCT t.assetId, t.assetName, t.assetStatus, t.assetType, t.assetAttributeInfo, t.coverFromDate, t.coverToDate
    FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS t(assetAttributeInfo, assetId, assetType, assetName, assetStatus, coverFromDate, coverToDate, isActive)
    WHERE LOWER(ip.policyAttributeInfo.policyNumber) LIKE LOWER('TEST ER_ABCD}-_%`~');","For this question, identifying comprehensive details of vessels insured under a given policy is crucial for effective asset tracking and operational planning. The `LOWER` operator is used because policy names contain special characters allowing for a case-insensitive comparison while preserving the special characters in them."
qa,Give me all policies of account with entityId = XYZ,"WITH CounterpartyInfo AS ( SELECT CAST(cp._id AS VARCHAR) AS counterparty_id FROM marine_live_data.questmarine_vesseldata_qa.counterparty cp WHERE CAST(cp._id AS VARCHAR) = 'XYZ' ) SELECT ip.policyAttributeInfo.policyNumber AS policy_name FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip JOIN CounterpartyInfo cpi ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)","For this question, you have to fetch policies of the account for which account ID is provided, always use CAST as VARCHAR while building query for account ID."
qa,Give me all vessels of account with entityId = XYZ,"WITH AccountInfo AS (SELECT CAST(cp._id AS VARCHAR) AS account_id FROM marine_live_data.questmarine_vesseldata_qa.counterparty cp WHERE CAST(cp._id AS VARCHAR) = 'XYZ'), VesselAssets AS (SELECT DISTINCT t.assetId, t.assetName FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip CROSS JOIN UNNEST(ip.insuredAssets) AS t(assetAttributeInfo, assetId, assetType, assetName, assetStatus, coverFromDate, coverToDate, isActive) JOIN AccountInfo ai ON ip.counterPartyId = ai.account_id) SELECT assetName, assetId FROM VesselAssets","For this question, you have to fetch vessels of the account for which account ID is provided, always use CAST as VARCHAR while building query for account ID."
qa,What are the ship status change and casualties and psc events happened in the month of may 2024 for account with entityId = XYZ,"SELECT CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_qa.counterparty cp
    WHERE CAST(cp._id AS VARCHAR) = 'XYZ'
),
VesselAssets AS (
    SELECT DISTINCT insuredAsset.assetId
    FROM marine_live_data.questmarine_vesseldata_qa.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset
    JOIN CounterpartyInfo cpi
        ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)
),
Events AS (
    SELECT
        e.associated_entity_id AS vessel_asset_id,
        e.event_type,
        e.event_date,
        e.event_data
    FROM marine_events_data.""enhanced-timeline"".events_raw_revised e
    JOIN VesselAssets va
        ON e.associated_entity_id = va.assetId
    WHERE e.event_date >= DATE '2024-05-01' AND e.event_date < DATE '2024-06-01'
    AND e.event_type IN ('SHIP_STATUS', 'CASUALTIES', 'PORT_STATE_CONTROL_NOT_DETAINED', 'PORT_STATE_CONTROL_DETAINED')
)
SELECT
    evs.event_type,
    evs.vessel_asset_id,
    evs.event_date,
    evs.event_data
FROM Events evs
ORDER BY evs.event_date DESC
LIMIT 10","for this question you have to first fetch vessels for the given account ID and then fetch respective events that happened for those vessels in the given time frame"
stage,Give ownership and management details of vessel with entityID = XYZ,"SELECT json_extract(event_data, '$.previous_value') AS previous_owner, json_extract(event_data, '$.current_value') AS current_owner, json_extract(event_data, '$.current_change_date') AS change_date FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE associated_entity_id = 'XYZ' AND event_type IN ('REGISTERED_OWNER', 'SHIP_MANAGER') ORDER BY event_date DESC LIMIT 10","when user asks for the details of ownership and management details  you have to extract and retrieve the previous owner, current owner, and change date from the event_data JSON field for 'REGISTERED_OWNER', 'SHIP_MANAGER' and order it by event_date"
stage,"An account in my portfolio has undergone multiple Port State Control (PSC)(max) inspections over the last year. Can you summarize the findings, including deficiencies or detentions, so that I can assess their potential impact on the vessel’s risk profile?","WITH PSCEvents AS (
    SELECT DISTINCT e.associated_entity_id AS vessel_imo
    FROM marine_events_data.""enhanced-timeline"".events_raw_revised e
    WHERE e.event_type IN ('PORT_STATE_CONTROL_DETAINED', 'PORT_STATE_CONTROL_NOT_DETAINED')
      AND e.event_date >= current_date - interval '1' year
),
AccountsWithPSC AS (
    SELECT DISTINCT ip.counterPartyId
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset
    JOIN PSCEvents psc
        ON insuredAsset.assetId = psc.vessel_imo
)
SELECT DISTINCT cp.name AS account_name
FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
JOIN AccountsWithPSC awp
    ON CAST(cp._id AS VARCHAR) = awp.counterPartyId
LIMIT 10","for this question, fetch all accounts with PSC events in last one year and create analysis and summary based on those events and vessels impacted"
stage,"Can you identify trends in JWLA entries for this account over the past 3 years, including changes in the frequency or duration of operations in these zones?","SELECT
    EXTRACT(YEAR FROM event_date) AS year,
    COUNT(*) AS jwla_zone_event_count
FROM
    marine_events_data.""enhanced-timeline"".events_raw_revised
WHERE
    event_type = 'JWLA_ZONES'
    AND associated_entity_id IN (
        SELECT
            CAST(imo AS VARCHAR)
        FROM
            marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary
        WHERE
            CAST(counterPartyId AS VARCHAR) = <ID of the respective account>
    )
    AND event_date >= current_date - interval '3' year
GROUP BY
    EXTRACT(YEAR FROM event_date)
ORDER BY
    EXTRACT(YEAR FROM event_date) DESC;","for this question, you have to fetch jwla zone count for the account and duration of vessels in jwla zones if available"
stage,"What is the correlation between Port State Control inspection results, jwla events, and major incidents involving this fleet for 2024?","SELECT
    e.event_date,
    e.event_type,
    e.event_data
FROM marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN (
    SELECT
        CAST(vessel_id AS VARCHAR) AS vessel_id
    FROM marine_events_data.""fleet_detail"".fleet_info,
         UNNEST(vessel_ids) AS t (vessel_id)
    WHERE id = <ID of respective fleet>
      AND tenant_id = <TENANT_ID of respective Tenant>
      AND status = 'ACTIVE'
) f
ON e.associated_entity_id = f.vessel_id
WHERE e.event_type IN ('PORT_STATE_CONTROL_DETAINED', 'PORT_STATE_CONTROL_NOT_DETAINED', 'JWLA_ZONES', 'CASUALTIES')
  AND e.event_date BETWEEN DATE '2024-01-01' AND DATE '2024-12-31'
ORDER BY e.event_date DESC
LIMIT 10;","for this question you have to check for psc, jwla zones and casualties event for vessels in the given fleet"
stage,"What is the trend of ownership changes across all vessels in this account for the past year, including the impact of these changes on associated product lines and exposures?","SELECT json_extract(event_data, '$.previous_value') AS previous_owner, json_extract(event_data, '$.current_value') AS current_owner, json_extract(event_data, '$.current_change_date') AS change_date, productlineId, netExposure FROM marine_events_data.""enhanced-timeline"".events_raw_revised JOIN marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary ON associated_entity_id = imo WHERE counterPartyId = '<ID of the account>' AND event_type = 'REGISTERED_OWNER' ORDER BY event_date DESC LIMIT 10","for this question, you have to fetch ownership changes for the vessels in the account and fetch product lines and exposure associated with the account"
stage,"Give me ports for the vessels in this fleet where Port State Control events has occurred frequently for past 2 years","WITH FleetVessels AS (
    SELECT CAST(vessel_id AS VARCHAR) AS vessel_id
    FROM marine_events_data.""fleet_detail"".fleet_info,
         UNNEST(vessel_ids) AS t (vessel_id)
    WHERE id = <ID of the respective fleet>
      AND tenant_id = <tenant_id of the respective tenant>
      AND status = 'ACTIVE'
),
PortStateControlEvents AS (
    SELECT
        JSON_EXTRACT_SCALAR(er.event_data, '$.inspection_port_decode') AS port_name,
        COUNT(*) AS detention_count
    FROM marine_events_data.""enhanced-timeline"".events_raw_revised er
    JOIN FleetVessels fv
        ON er.associated_entity_id = fv.vessel_id
    WHERE er.event_type IN ('PORT_STATE_CONTROL_DETAINED', 'PORT_STATE_CONTROL_NOT_DETAINED')
      AND JSON_EXTRACT_SCALAR(er.event_data, '$.ship_detained') = 'true'
      AND er.event_date >= date_add('year', -2, current_date)
    GROUP BY JSON_EXTRACT_SCALAR(er.event_data, '$.inspection_port_decode')
)
SELECT port_name, detention_count
FROM PortStateControlEvents
ORDER BY detention_count DESC
LIMIT 10;","for this question, you have to fetch ports and count where Port state control events has occurred for the vessels in the respective fleet"
stage,"what are the events happened in the month of dec 2024 in this account {entity_name = 'XYZ' } with account ID {entity_id = '123'}","WITH CounterpartyInfo AS (SELECT CAST(cp._id AS VARCHAR) AS counterparty_id FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp WHERE CAST(cp.name AS VARCHAR) = 'XYZ'), VesselAssets AS (SELECT DISTINCT insuredAsset.assetId FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip CROSS JOIN UNNEST(ip.insuredAssets) AS insuredAsset JOIN CounterpartyInfo cpi ON ip.counterPartyId = CAST(cpi.counterparty_id AS VARCHAR)), Events AS (SELECT e.associated_entity_id AS vessel_asset_id, e.event_type, e.event_date, e.event_data FROM marine_events_data.""enhanced-timeline"".events_raw_revised e JOIN VesselAssets va ON e.associated_entity_id = va.assetId WHERE e.event_date >= DATE '2024-12-01' AND e.event_date < DATE '2025-01-01') SELECT evs.event_type, evs.vessel_asset_id, evs.event_date, json_extract(evs.event_data, '$') AS event_details FROM Events evs ORDER BY evs.event_date DESC LIMIT 10","for this question you have to fetch events for the accounts, first fetch vessels of the given account then fetch events of those vessels"
stage,"A vessel in this account with account ID {entity_id = 123} has switched P&I Clubs. List the names of all the new P&I Clubs added, so that I can review if they have the reputation for covering higher-risk vessels.","SELECT EXTRACT(YEAR FROM event_date) AS year, COUNT(*) AS jwla_zone_event_count FROM marine_events_data.""enhanced-timeline"".events_raw_revised WHERE event_type = 'JWLA_ZONES' AND associated_entity_id IN (SELECT CAST(imo AS VARCHAR) FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary WHERE CAST(counterPartyId AS VARCHAR) = '123') AND event_date >= current_date - interval '3' year GROUP BY EXTRACT(YEAR FROM event_date) ORDER BY EXTRACT(YEAR FROM event_date) DESC","for questions related to events related to account you have to fetch vessels of the given account and then fetch event data for events timeline db"
stage,When was the last claim filed for the vessel IMO 1234567?,"SELECT
    cd.vesselImo,
    cd.vesselName,
    MAX(date(from_unixtime(cd.loss_date / 1000))) AS last_filing_date
FROM marine_live_data.questmarine_analyticsdata_stage.claim_details cd
WHERE cd.vesselImo = '1234567'
GROUP BY cd.vesselImo, cd.vesselName;",for this you have to fetch the claim date of policy for the vessel form event_data field
stage,What is the total incurred value for account ABCD in the last 6 months?,"WITH CounterpartyInfo AS (
    SELECT
        CAST(cp._id AS VARCHAR) AS counterparty_id,
        cp.name AS counterparty_name
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE cp.name = 'ABCD'
)
SELECT
    ci.counterparty_name,
    SUM(CAST(cd.total_incurred AS DOUBLE)) AS total_incurred_value,
    cd.totalIncurredCurrencyCode AS incurred_value_currency
FROM marine_live_data.questmarine_analyticsdata_stage.claim_details cd
JOIN CounterpartyInfo ci
    ON cd.counterPartyId = ci.counterparty_id
WHERE date(from_unixtime(cd.loss_date / 1000)) >= DATE_ADD('month', -6, CURRENT_DATE)
GROUP BY ci.counterparty_name, cd.totalIncurredCurrencyCode",for this you have to fetch the vessels of the account and then check for claims filed for those vessels in last 6 months and give total sum of incurred value
stage,What is the cover value for policy ABCD on the vessel IMO 1234567?,"WITH PolicyInfo AS (
    SELECT
        ip.policyAttributeInfo.policyNumber AS policy,
        ip.counterPartyId AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.insurance_policy ip
    WHERE ip.policyAttributeInfo.policyNumber = 'ABCD'
)
SELECT
    pi.policy AS policy,
    vps.imo AS vessel_imo,
    vps.totalSumInsured AS total_sum_insured
FROM marine_live_data.questmarine_vesseldata_stage.vessel_policy_summary vps
JOIN PolicyInfo pi
    ON vps.counterPartyId = pi.counterparty_id
WHERE vps.imo = '1234567';",for this you have to fetch policy number and vessel and then fetch total sum insured as cover value for that vessel in policy
stage,How many claims have been incurred in ABCD in the last 2 years?,"WITH CounterpartyInfo AS (
    SELECT
        CAST(cp._id AS VARCHAR) AS counterparty_id,
        cp.name AS counterparty_name
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE cp.name = 'ABCD'
)
SELECT
    DISTINCT COUNT(cd._id) AS claim_count
FROM marine_live_data.questmarine_analyticsdata_stage.claim_details cd
JOIN CounterpartyInfo ci
    ON cd.counterPartyId = ci.counterparty_id
WHERE date(from_unixtime(cd.loss_date / 1000)) >= DATE_ADD('year', -2, CURRENT_DATE);",for this you have to fetch total count of claims filed for that account for given time period
stage,Which vessels had their class withdrawn in the past year and were they later re-classed or scrapped?,"SELECT e.associated_entity_id, e.event_type, e.event_data, e.event_date
FROM marine_events_data.""enhanced-timeline"".events_raw_revised e
WHERE e.event_type = 'SOCIETY'
AND e.event_date >= date_trunc('year', current_date) - interval '1' year
ORDER BY e.event_date DESC
LIMIT 10","for this question where asked for vessels with their class withdrawn you should fetch vessels with event society for given time frame"
stage,Which vessels in my portfolio have recently changed class or been class withdrawn and what are the potential risks?,"SELECT e.associated_entity_id, e.vessel_name, e.event_type, e.event_data, e.event_date
FROM marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN marine_live_data.questmarine_vesseldata_stage.vessel_summary v
ON e.associated_entity_id = v._id
WHERE e.event_type IN ('SOCIETY', 'SOCIETY_DISCLASSED')
AND v.assetStatus IN ('OnCover', 'OffCover')
AND e.event_date >= date_trunc('year', current_date) - interval '1' year
ORDER BY e.event_date DESC
LIMIT 10","for this question where class change and class withdrawn both cases are involved you have to check for events society and society disclassed"
stage,Are there any vessels currently on cover that have undergone significant operational changes (e.g. new routes ownership change re-flagging) that might require a policy adjustment?,"SELECT e.associated_entity_id, e.vessel_name, e.event_type, e.event_date
FROM marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN marine_live_data.questmarine_vesseldata_stage.vessel_summary v
ON e.associated_entity_id = v._id
WHERE e.event_type IN ('OPERATOR', 'GROUP_BENEFICIAL_OWNER', 'VESSEL_FLAG')
AND v.assetStatus = 'OnCover'
AND e.event_date >= date_trunc('year', current_date) - interval '1' year
ORDER BY e.event_date DESC
LIMIT 10","for this question you have to fetch events for operator changes, group beneficial owner and vessel flag changes for on cover vessels"
stage,"Which off-cover vessels had frequent claims or high-risk indicators before their policies lapsed, and should we actively avoid them in the future?","SELECT c.vesselImo, c.vesselName, c.claim_type, c.loss_date FROM marine_live_data.questmarine_analyticsdata_stage.claim_details c JOIN marine_live_data.questmarine_vesseldata_stage.vessel_summary v ON c.vesselImo = v._id WHERE v.assetStatus = 'OffCover' ORDER BY c.loss_date DESC LIMIT 10","for this question you have to fetch vessels which had most claims and are off cover"
stage,"We’ve got a legal dispute ongoing for a machinery failure claim. Are there any details within Casualty or Port State Control data that might indicate ongoing issues that were not addressed by the owner?","SELECT
  e.associated_entity_id AS imo,
  e.event_type,
  COUNT(*) AS occurrences
FROM
  marine_events_data.""enhanced-timeline"".events_raw_revised e
JOIN
  marine_live_data.questmarine_analyticsdata_stage.claim_details cd
ON e.associated_entity_id = cd.vesselImo
WHERE
  e.event_type IN ('CASUALTIES', 'PORT_STATE_CONTROL_DETAINED')
GROUP BY
  e.associated_entity_id,
  e.event_type
ORDER BY
  occurrences DESC
LIMIT 10","for this you have to look for vessels for which claims have been filed and has had casualty and psc events"
stage,"Which vessels in account ABC have had multiple claims in the last 5 years, and what are the common causes?","WITH AccountInfo AS (
    SELECT
        CAST(cp._id AS VARCHAR) AS counterparty_id
    FROM marine_live_data.questmarine_vesseldata_stage.counterparty cp
    WHERE cp.name = 'ABC'
),
ClaimCounts AS (
    SELECT
        cd.vesselImo,
        COUNT(cd._id) AS claim_count
    FROM marine_live_data.questmarine_analyticsdata_stage.claim_details cd
    JOIN AccountInfo ai ON cd.counterPartyId = ai.counterparty_id
    WHERE date(from_unixtime(cd.loss_date / 1000)) >= DATE_ADD('year', -5, CURRENT_DATE)
    GROUP BY cd.vesselImo
    HAVING COUNT(cd._id) > 1
)
SELECT
    cd.vesselName,
    cd.vesselImo,
    cd.claim_type,
    cd.claimDescription
FROM marine_live_data.questmarine_analyticsdata_stage.claim_details cd
JOIN ClaimCounts cc ON cd.vesselImo = cc.vesselImo
WHERE date(from_unixtime(cd.loss_date / 1000)) >= DATE_ADD('year', -5, CURRENT_DATE)","for this question you have to fetch vessels details with most claims in the given account in last 5 years"
stage,"Can you rank recent claims based on severity and highlight potential high-value losses?","SELECT
    cd.vesselName,
    cd.vesselImo,
    cd.claim_type,
    CAST(cd.total_incurred AS DOUBLE) AS claim_value,
    cd.totalIncurredCurrencyCode AS incurred_value_currency
FROM
    marine_live_data.questmarine_analyticsdata_stage.claim_details cd
WHERE
    date(from_unixtime(cd.loss_date / 1000)) >= DATE_ADD('month', -6, CURRENT_DATE)
ORDER BY
    claim_value DESC
LIMIT 10;","for this question you have to fetch recent claims data with high incurred values"