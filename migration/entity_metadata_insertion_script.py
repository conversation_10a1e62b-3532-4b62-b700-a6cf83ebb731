from sqlalchemy import create_engine, Column, String
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# Define the PostgreSQL connection URL
DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/platform_ai_service'
# DATABASE_URL = os.getenv('DATABASE_URL')

# Create a database engine
engine = create_engine(DATABASE_URL)

# Create a base class for your model
Base = declarative_base()

class EntityInfo(Base):
    __tablename__ = 'entity_metadata'

    entity_type = Column(String, primary_key=True)
    entity_id = Column(String)
    entity_name = Column(String)

# Create a session
Session = sessionmaker(bind=engine)
session = Session()

# List of entities to be inserted
LIST_ENTITIES = [
    {
        "entity_type": "vessel",
        "entity_id": "IMO of vessel, used as associated_entity_id. So always consider this when asked for vessels, vessels inside account and fleet.",
        "entity_name": "Name of vessel"
    },
    {
        "entity_type": "fleet",
        "entity_id": "Unique ID of a fleet. Always use this ID for making queries when question asked about a specific fleet.",
        "entity_name": "Name of fleet"
    },
    {
        "entity_type": "account",
        "entity_id": "Unique ID of an account also known as counterPartyId. Always use this for making queries using CAST as varchar and answering questions asked for a specific account and entity_type is account.",
        "entity_name": "Name of account"
    }
]

def insert_entity_info(entity_list):
    try:
        # Clear the table before inserting new data (if needed)
        session.query(EntityInfo).delete()

        for entity_dict in entity_list:
            entity_meta = EntityInfo(
                entity_type=entity_dict["entity_type"],
                entity_id=entity_dict["entity_id"],
                entity_name=entity_dict["entity_name"],
            )
            session.add(entity_meta)

        # Commit the transaction once after adding all entities
        session.commit()

        print(f"Inserted {len(entity_list)} entities successfully.")

    except Exception as error:
        print(f"Error inserting data: {error}")
        session.rollback()
    finally:
        # Close the session
        session.close()

# Call the function to insert entity information
insert_entity_info(LIST_ENTITIES)