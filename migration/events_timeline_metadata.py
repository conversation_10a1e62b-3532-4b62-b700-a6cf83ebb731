metadata = {
  "catalogs": [
    {
      "name": "marine_events_data",
      "connector_type": "POSTGRES",
      "description":"""
            Events timeline DB
            So in case you need information related to 
            vessels in a portfolio, first go to the tenant-specific datasource, i.e., marine_live_data, and fetch vessels matching that portfolio.
            Then use those IMOs and query for events here. You should perform a JOIN by vessel IMO.
            Always create the query using "Group By" Vessel IMO and not just vessel name even if question is asked for vessel name.
            Always give vessel IMOs when question related to events type is asked. Like list of casualties that took place for account, for question like this provide vessel imo also in response.
            """,

      "databases": [
        {
          "name": "enhanced_timeline",
          "description": "Database containing events data for vessels. This database has all event data like event name, event type etc for vessels.",
          "tables": [
            {
                "name": "events_raw_revised",
                "description": """
               - Raw data of events that occurred for all vessels
               - Each event includes event_type, event_category (which maps to category_event_types),
                 event_data (which is JSON) and associated_entity (which is the vessel),
                 use JSON operators to build query on JSON fields
         """,
              "columns": [
                {"name": "event_date", "type": "date",
                 "description": "Date of the event. Dates in response should always be formatted as DD Month YYYY, such as 27 December 2023 or 01 January 2024."},
                {"name": "associated_entity_id", "type": "text",
                 "description": "Vessel IMO. Use this column for joining as it is unique. When providing the name and IMO number of a vessel in response, you should always format it like this in response: Vessel Name | IMO Number. For example: CMA CGM Alexander von Humboldt | IMO 9454448."},
                {"name": "vessel_name", "type": "text",
                 "description": "Contains vessel name. Never use this column for joining as multiple IMOs have same name. When providing the name and IMO number of a vessel in response, you should always format it like this in response: Vessel Name | IMO Number. For example: CMA CGM Alexander von Humboldt | IMO 9454448."},
                {"name": "vessel_type", "type": "text", "description": "Contains type of vessel"},
                {"name": "vessel_type_group", "type": "text",
                 "description": "Contains group type  of vessel. Maps to vessel_type"},
                {"name": "event_type", "type": "text", "description": """
                             Type of the event, indicating the specific action or occurrence related to a vessel. More events like CASUALTIES or JWLA_ZONES means increased risk.
                             Convert these event classifications into more human-readable formats in response by removing underscores, capitalizing appropriately, and ensuring clarity, such as transforming them into:
                             - Ship Status
                             - Casualties
                             - JWLA Zones
                             - Port State Control, etc
                             Possible event types include:
                             - **SHIP_TYPE**: Indicates a change in the type of the ship. Consequence: May affect vessel classification and operational usage.
                             - **SOCIETY_DISCLASSED**: All vessels that have class change, involves changes in the classification society. Consequence: Can impact certification and compliance.
                             - **SOCIETY**: Any vessels that have Class withdrawn or Class suspended. Consequence: Can impact certification and compliance.
                             - **SHIP_MANAGER**: Reflects updates to the ship's manager. Consequence: Changes in management responsibilities and reporting.
                             - **OPERATOR**: Represents updates to the ship's operator. Consequence: Affects operational management and contractual obligations.
                             - **NAME_CHANGES**: Captures changes to the vessel's name. Consequence: Impacts documentation and registration.
                             - **SHIP_CERTIFICATES**: Involves issuance or renewal of ship certificates. Consequence: Affects vessel's legal and operational status.
                             - **SHIP_STATUS**: Reflects changes in the ship's operational status. Consequence: Indicates whether the vessel is active, inactive, or scrapped.
                             - **VESSEL_FLAG**: Indicates changes in the vessel's flag state. Consequence: Affects legal jurisdiction and operational constraints.
                             - **PI_CLUB**: Relates to changes in the vessel's P&I Club. Consequence: Affects insurance coverage and liability.
                             - **TECHNICAL_MANAGER**: Involves changes to the technical management of the vessel. Consequence: Affects technical oversight and maintenance.
                             - **CASUALTIES**: Details any casualties or incidents involving the vessel. Consequence: Impacts safety records and potential legal implications. More of this event means increased risk.
                             - **DISPOSALS**: Relates to the disposal of the vessel. Consequence: Affects records and compliance with disposal regulations.
                             - **JWLA_ZONES**: Involves operations within Joint War Risk Committee zones. Consequence: Impacts insurance and operational risk management. More of this event means increased risk.
                             - **PORT_STATE_CONTROL_NOT_DETAINED**: Covers Port State Control inspections without any detention. Consequence: Affects vessel compliance with international regulations.
                             - **PORT_STATE_CONTROL_DETAINED**: Covers Port State Control  detention. Consequence: Affects vessel compliance with international regulations.
                             - **GROUP_BENEFICIAL_OWNER**: Indicates changes to the group beneficial owner of the vessel. Consequence: Affects ownership records and control.
                             - **REGISTERED_OWNER**: Reflects changes to the registered owner of the vessel. Consequence: Impacts ownership documentation and legal status.  
                         """},
                {"name": "event_category", "type": "text", "description": """
                                The broader category to which the event belongs. This is a hierarchical structure where events are 
                                grouped under parent categories. Examples include:
                                - **REGISTRATION_CLASS_PI**: Events related to the vessel's registration and classification.
                                - **OWNERSHIP_MANAGEMENT**: Events related to changes in ownership or management.
                                - **TECHNICAL_DETAILS**: Events related to the technical details or specifications of the vessel.
                                - **OPERATIONAL_STATUS**: Events related to the operational status of the vessel, including any incidents.
                                - **CASUALTIES_CLAIMS**: Events related to casualties and claims.
                                - **OPERATIONS**: Events related to operational zones and inspections.
                                - **PORT_STATE_CONTROL**: Events related to Port State Control inspections.
                            """},
                {"name": "event_data", "type": "json", "description": """
                               Detailed data associated with the event, stored as JSON. Use JSON operators like 'json_extract' while building query for json field. This field can include a wide range of information depending on the event type.
                               For CASUALTIES events all lat/long coordinates should be provided to 6 decimal places and add a link to the map for specified location in response
                               Possible keys include:
                               - **SHIP_TYPE**: `current_value`, `previous_value`, `current_change_date`, `previous_change_date`.
                               - **SOCIETY**: `class`, `reason`.
                               - **SOCIETY_DISCLASSED**: `class`, `class_indicator`.
                               - **SHIP_MANAGER**: `current_value`, `previous_value`, `current_change_date`, `previous_change_date`.
                               - **OPERATOR**: `current_value`, `previous_value`, `current_change_date`, `previous_change_date`.
                               - **NAME_CHANGES**: `VESSEL_NAME`, `current_value`, `previous_value`, `current_change_date`, `previous_change_date`.
                               - **SHIP_CERTIFICATES**: `certificate_title`, `issuing_authority`, `valid_from`, `valid_to`.
                               - **SHIP_STATUS**: `STATUS`, `current_value`, `previous_value`, `current_change_date`, `previous_change_date`.
                               - **VESSEL_FLAG**: `VESSEL_FLAG`, `current_value`, `previous_value`, `current_change_date`, `previous_change_date`.
                               - **PI_CLUB**: `PANDI`, `current_value`, `previous_value`, `current_change_date`, `previous_change_date`.
                               - **TECHNICAL_MANAGER**: `current_value`, `previous_value`, `TECHNICAL_MANAGER`, `current_change_date`, `previous_change_date`.
                               - **CASUALTIES**: `casualty_type`, `start_latitude`, `start_longitude`, `severity_indicator`, `weather_at_time_of_incident`, `casualty_or_demolition_indicator`.
                               - **DISPOSALS**: `ship_breaker`, `disposal_date`.
                               - **JWLA_ZONES**: `multi_entry`, `geo_area_name`, `distinct_port_count`, `zone_entry_date`, `zone_exit_date`.
                               - **PORT_STATE_CONTROL_DETAINED**: `country`, `unlocode`, `authorisation`, `port_latitude`,`port_longitude`, `ship_detained`, `expanded_inspection`, `inspection_port_decode`, `number_of_defects`, `number_of_days_detained`, `followup_inspection`.
                               - **PORT_STATE_CONTROL_NOT_DETAINED**: `country`, `unlocode`, `authorisation`, `port_latitude`,`port_longitude`, `ship_detained`, `expanded_inspection`, `inspection_port_decode`,`number_of_defects`, `followup_inspection`.
                               - **GROUP_BENEFICIAL_OWNER**: `current_value`, `previous_value`, `current_change_date`, `previous_change_date`, `GROUP_BENEFICIAL_OWNER`.
                               - **REGISTERED_OWNER**: `current_value`, `previous_value`, `current_change_date`, `previous_change_date`, `REGISTERED_OWNER`.
                           """}
              ]

            }
          ]
        },
        {
          "name": "fleet_detail",
          "description": "Database containing fleet information, including fleet names, vessel IDs, and their statuses.",
          "tables": [
            {
              "name": "fleet_info",
              "description": "Table containing fleet information.",
              "columns": [
                { "name": "fleet_name", "type": "text", "description": "Name of the fleet." },
                { "name": "id", "type": "text", "description": "ID of the fleet. **Never disclose this ID in the response.**" },
                { "name": "tenant_id", "type": "text", "description": "ID of the tenant associated with the fleet. Always check for tenant_id while making a query.** Never disclose this id in the response **. For tenant dev take tenantId associated with tenant dev and for tenant stage  take tenantId associated with tenant stage and for tenant qa take tenantId associated with tenant qa" },
                { "name": "vessel_ids", "type": "array<BIGINT>", "description": "IDs of vessels associated with the fleet.\n you have to check all the vessels_ids for given fleet and map it to associated_entity_id\nWhen providing the name and IMO number of a vessel in response, you should always format it like this in response: Vessel Name | IMO Number. For example: CMA CGM Alexander von Humboldt | IMO 9454448." },
                { "name": "status", "type": "text", "description":"Status of the fleet. only include fleet with status 'ACTIVE'. Do not consider other status of fleet" },
                { "name": "vessel_count", "type": "bigint", "description": "Contains count of vessels." }
              ]
            }
          ]
        }
      ]
    }
  ]
}
