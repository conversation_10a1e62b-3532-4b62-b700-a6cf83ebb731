from langchain_anthropic import <PERSON>t<PERSON>nt<PERSON>
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_google_vertexai import ChatVertexAI
from langchain_openai import ChatOpenAI


# Note that the docstrings here are crucial, as they will be passed along
# to the model along with the class name.

class list_datasources(BaseModel):
    """
    List datasources that will help answer the user's question.
    Use meta-data from table to identify the purpose
    The datasources might be MONGO, POSTGRES.
    This information will be needed to further generate query
    """


class list_databases_for_datasource(BaseModel):
    """
    List databases in specific datasource.
    Based on the database meta-data, choose most apt database
    This may represent Mongo database or Postgres Database
    """
    datasource_id: str = Field(..., description="The UUID of the DataSource")


class list_tables_for_database(BaseModel):
    """
    List tables in specific database
    that will help answer the user's question.
    Use meta-data from table to identify the purpose
    """
    database_id: str = Field(..., description="The UUID of the DataBase")


class list_columns_for_table(BaseModel):
    """
    Get information about a table structure ie columns,
     including the description that will help answer the user's question.
    """
    table_id: str = Field(..., description="The UUID of the table")


class execute_query(BaseModel):
    """Execute the query on given datasource, database and table to get answer"""
    datasource_url: str = Field(...,
                                description="The URL of database like Mongo/Postgres. This should be in dataSource table")
    datasource_type: str = Field(..., description="The type of Datasource like Mongo/Postgres")
    database_name: str = Field(..., description="The name of database on which query is to be executed")
    table_name: str = Field(..., description="The name of table on which query is to be executed")
    query: str = Field(..., description="query that will help give quantitative answers to the user's question")


class plot_data_and_save_file(BaseModel):
    """
    Execute the code provided by the LLM to generate data visualization using matplotlib library
    Save the generated figure in `svc_output_data` folder under same project directory
    ie in {PROJECT_ROOT_DIRECTORY}/svc_output_data/{unique_file_name}

    Name the file uniquely using uuid


    **Return the file path in JSON format**
    {
     "file_path" : <GENERATED FILE PATH>
    }

    Returns:
            str: Path to the generated image file.
    """

    code: str = Field(...,
                      description="The code created by LLM to generate data visualization using matplotlib")


tools = [
    list_datasources,
    list_databases_for_datasource,
    list_tables_for_database,
    list_columns_for_table,
    execute_query,
    plot_data_and_save_file
]

##https://platform.openai.com/docs/models/gpt-3-5
GPT_35 = "gpt-3.5-turbo (gpt-3.5-turbo-0125)"
GPT_4O = "gpt-4o (gpt-4o)"
GPT_4O_MINI = "gpt-4o-mini (gpt-4o-mini)"


def get_llm(llm_name=None):
    if llm_name == GPT_4O:
        return getGPT_4_TURBO()
    if llm_name == GPT_35:
        return getGPT_3_5_TURBO()
    if llm_name == GPT_4O_MINI:
        return getGPT_4_O_MINI
    return getGPT_4_O_MINI()


def getGemini_1_5():
    llm = ChatVertexAI(model="gemini-1.5-pro-preview-0409", temperature=0, max_output_tokens=8192)
    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools


def getGemini_1_0():
    llm = ChatVertexAI(model="gemini-1.0-pro-001", temperature=0, max_output_tokens=8192)
    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools


def getGPT_3_5_TURBO():
    llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0)
    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools


def getGPT_4_TURBO():
    llm = ChatOpenAI(model="gpt-4o", temperature=0)
    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools


def getGPT_4_O_MINI():
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools


def get_claude_v3_haiku_llm_object():
    llm = ChatAnthropic(model="claude-3-haiku-20240229")

    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools


def get_claude_v3_sonnet_llm_object():
    llm = ChatAnthropic(model="claude-3-sonnet-20240229")

    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools


def get_claude_v3_opus_llm_object():
    llm = ChatAnthropic(model="claude-3-opus-20240229")

    llm_with_tools = llm.bind_tools(tools)
    return llm_with_tools
