import random

from anthropic import <PERSON>thropicVert<PERSON>
from langchain_anthropic import Chat<PERSON>nt<PERSON>
from langchain_google_vertexai import Chat<PERSON>ertexA<PERSON>
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from config.app_config import Config
from utils.LogUtils import LOG
from utils.Tiktoken import execution_timer

# Cache for LLM instances
_llm_cache = {}


@execution_timer
def get_llm_object(llm_name):
    # Check cache first
    if llm_name in _llm_cache:
        return _llm_cache[llm_name]

    try:
        # Split only once
        parts = llm_name.split('_', 1)
        if len(parts) != 2:
            LOG.warning(f"Invalid LLM name format: {llm_name}")
            return None

        llm_provider, model_name = parts
        llm_provider = llm_provider.lower()  # Convert once
        max_token = 8192
        temp = 0.0

        # Create LLM instance based on provider
        llm = None
        if llm_provider == 'openai':
            llm = ChatOpenAI(model=model_name, temperature=temp)
        elif llm_provider == 'anthropic':
            llm = ChatAnthropic(model_name=model_name, temperature=0.27,
                                top_k=random.randint(480, 500), max_tokens=max_token)
        elif llm_provider == 'geminiai_temp':
            llm = ChatVertexAI(model=model_name, temperature=temp, max_tokens=max_token,
                               max_retries=3, stop=None, location=Config.GCP_PROJECT_REGION)
        elif llm_provider == 'geminiai':
            llm = ChatGoogleGenerativeAI(model=model_name, temperature=temp, max_tokens=max_token)
        elif llm_provider == 'vertexai':
            llm = AnthropicVertex(region="us-east5", project_id=Config.GCP_PROJECT_ID)

        # Cache the instance
        if llm:
            _llm_cache[llm_name] = llm

        return llm
    except Exception as e:
        LOG.warning(f"Unable to load LLM: {e}")
        return None
