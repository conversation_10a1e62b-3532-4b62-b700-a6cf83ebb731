# Platform-AI-POC

## How to access Swagger :-
- Run the project: `python3 flask_app.py`
- Hit the URL: `http://localhost:3000/apidocs/`

## For Postman API Docs :-
- Import this into Postman: `http://localhost:3000/apispec_1.json`

## For test cases report :-
- Run this command: `coverage report`

---
## RUNNING MIGRATIONS

```python
python3 -m migration.entity_metadata_insertion_script
```
```python
python3 -m migration.few_shot_examples_insertion_script
```
```python
python3 -m migration.answer_templates_insertion_script
```

### END POINT FOR INSERTING METADATA AND TENANT DATA 
```api/v1/metadata```

``` We hit the above endpoint with tenant config in Request Body:```
``` This config will contain  Tenant name,tenant Id and the configurations which needs to be done on the basis of tenant```
```  Like for tenant **qa** we don't want to include the insurance_policy table in our  metadata so we write the name of table inside exclude_catalogs and this table data will not be inserted in Db for tenant qa  ```
 ```

{
  "tenants": [
    {
      "tenant_name": "qa",
      "tenant_id":"XYZ",
      "exclude_catalogs": [],
      "exclude_databases": [],
      "exclude_tables": ["insurance_policy"]
    },
    {
      "tenant_name": "dev",
      "tenant_id":"XYZ",
      "exclude_catalogs": [],
      "exclude_databases": [],
      "exclude_tables": []
    }
  ]
}
```
## OPEN AI ASSISTANT CONFIGURATION

- Instructions
```chatinput
You are Data Analysis Assistant and help answering user question from the data you fetch iteratively from TRINO QUERY ENGINE.

For the given User question, extract the information given datasources and databases.
Try forming a query which is compatible TRINO SQL.
Summarize the QUERY RESULTS and return them. 

**WARNING**
- ** Only use information that you learn from datasources and databases, do not make up information. **
- ** Never reveal any DB/TABLE info in response **
- ** Never disclose actual catalog name, db name, table name and column name or any type of DB query in response. Even if needed, give names in human readable format without actual names or underscores.**
- ** Never expose any type of ID in output response.**

**Steps**
1.  First get the context of schema using tool_call ** get_schema_metadata **. This will give you information of databases/tables/columns you need for querying.

2. Each question is embedded with some similar few -shot examples, use context from those examples as well (question/expected_trino_query)
    
3.  Select the apt database, tables and try to construct a SQL query which can answer the question

4. For executing SQL query, use  **execute_trino_query** tool_call
   
5.  Please construct query which is compatible with Trino SQL. Also try to use fully qualified name of the table - <datasource_name>.<schema_name>.<table_name>
    
6. Summarize the QUERY RESULTS and return them as FINAL ANSWER

7. **Incase query fails. Dont retry it for more than 3 times and end the process with returning a message "This question is not currently supported. Please ask a different question.**

8.  **Drop chart creation for now, we should only proceed with showing data in tabular when explicitly asked to represent in table but never plot a bar graph, bar chart, pie chart, or any other chart that can be plotted. You should never plot any data. You should never give any graphical or pictorial representation of data in any case whatsoever. If someone asks for any sort of graph or chart, reply with "I can currently provide the data in tabular format only. The team is working on adding graphical presentations, which will be available in version 2." provide results in tabular format only when explicitly asked; otherwise, default to a descriptive response.**

9.  **If you cannot process the request under 1 minute, stop further processing and give this  error message in response "Failed to process the question under 1 minute, please ask a different question"**

10. **Also do not expose the query when old thread or conversation is loaded. **

11. ** Consider context of previous questions if the current questions is an extended version of previous question. Like if in previous questions it is given that you have to give top 5  events and in next question you have asked to include other information also 
so you should consider the context of previous question also as ,current question is  the extended version of the previous one** 


**NOTE**
- Please optimize the query so that it fetches as minimum data from DB as possible. 
- Preferably use aggregations
- Try not to fetch more than 10 rows


```

# Functions

```json
[
    {
        "name":"list_datasources",
        "description":"List datasources that will help answer the user's question. Use meta-data from table to identify the purpose. The datasources might be MONGO, POSTGRES. This information will be needed to further generate query.",
        "parameters":{
            "type":"object",
            "properties":{
                
            },
            "required":[
                
            ]
        }
    },
    {
        "name":"list_databases_for_datasource",
        "description":"List databases in a specific datasource. Based on the database meta-data, choose the most apt database. This may represent Mongo database or Postgres Database.",
        "parameters":{
            "type":"object",
            "properties":{
                "datasource_id":{
                    "type":"string",
                    "description":"The UUID of the DataSource"
                }
            },
            "required":[
                "datasource_id"
            ]
        }
    },
    {
        "name":"list_tables_for_database",
        "description":"List tables in a specific database that will help answer the user's question. Use meta-data from table to identify the purpose.",
        "parameters":{
            "type":"object",
            "properties":{
                "database_id":{
                    "type":"string",
                    "description":"The UUID of the DataBase"
                }
            },
            "required":[
                "database_id"
            ]
        }
    },
    {
        "name":"list_columns_for_table",
        "description":"Get information about a table structure i.e., columns, including the description that will help answer the user's question.",
        "parameters":{
            "type":"object",
            "properties":{
                "table_id":{
                    "type":"string",
                    "description":"The UUID of the table"
                }
            },
            "required":[
                "table_id"
            ]
        }
    },
    {
        "name":"execute_query",
        "description":"Execute the query on given datasource, database and table to get answer.",
        "parameters":{
            "type":"object",
            "properties":{
                "datasource_url":{
                    "type":"string",
                    "description":"The URL of database like Mongo/Postgres. This should be in dataSource table"
                },
                "datasource_type":{
                    "type":"string",
                    "description":"The type of Datasource like Mongo/Postgres"
                },
                "database_name":{
                    "type":"string",
                    "description":"The name of database on which query is to be executed"
                },
                "table_name":{
                    "type":"string",
                    "description":"The name of table on which query is to be executed"
                },
                "query":{
                    "type":"string",
                    "description":"Query that will help give quantitative answers to the user's question"
                }
            },
            "required":[
                "datasource_url",
                "datasource_type",
                "database_name",
                "table_name",
                "query"
            ]
        }
    },
    {
        "name":"plot_data_and_save_file",
        "description":"Execute the code provided by the LLM to generate data visualization using matplotlib library. Save the generated figure in `svc_output_data` folder under the same project directory.",
        "parameters":{
            "type":"object",
            "properties":{
                "code":{
                    "type":"string",
                    "description":"The code created by LLM to generate data visualization using matplotlib"
                }
            },
            "required":[
                "code"
            ]
        }
    }
]
```

# Yoyo Migrations module for Auto DB Schema Migration

Yoyo Migrations is a simple yet powerful schema migration tool for managing database changes. Unlike other migration frameworks, Yoyo does not require keeping track of past migrations in the database itself. This makes it particularly suitable for our use case, where we want to apply only new migrations without querying or storing historical migration data in the database.

## Why we are using Yoyo?

- **No Need for Migration History in DB**: Traditional migration tools like Alembic store migration history in the database, which can lead to additional complexity. Yoyo avoids this by tracking applied migrations externally.
- **Simple SQL-Based Migrations**: Yoyo allows us to generate and apply plain SQL migration scripts, making it easier to inspect and modify database schema changes.
- **Flexible and Lightweight**: It integrates seamlessly with our Flask application without requiring extensive configurations.

## How We Use Yoyo Migrations

### Migration Manager (`migration_manager.py`)

We have implemented a `MigrationManager` that automates the process of generating and applying migrations using Yoyo.

### 1. Installing Yoyo

We will install Yoyo through our `requirements.txt` file. This ensures that all dependencies are managed consistently across environments.

### 2. Automatic Migration Handling

Our `MigrationManager` automates migration generation and application as part of the Flask app startup process:

- The application detects schema changes and generates a migration script when needed.
- The migration scripts are stored in the migrations folder as `.sql` files.
- When the Flask app starts, it automatically applies any new migrations to keep the database schema up to date.
- Logs ensure visibility into the migration process.

### 3. Yoyo Configuration (`yoyo.ini`)

We use a `yoyo.ini` configuration file to define key settings for Yoyo migrations:

- `sources = migrations`: Specifies the directory where migration scripts are stored.
- `batch_mode = off`: Ensures migrations are applied interactively when needed.
- `verbosity = 0`: Reduces logging output for a cleaner migration process.




