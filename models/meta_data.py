import uuid

from datetime import datetime
from marshmallow import fields
from models import db, ma
from models.enums import DataStoreType


# Represents metadata for a Trino catalog. Each catalog contains information
# about its connector type, description, and the tenant it belongs to.
class TrinoCatalogMetaData(db.Model):
    __tablename__ = 'trino_catalog_metadata'
    name = db.Column(db.String(255), nullable=False)
    connector_type = db.Column(db.Enum(DataStoreType))
    description = db.Column(db.Text, nullable=False)
    tenant_name = db.Column(db.String(36), nullable=False)
    createdAt = db.Column(db.DateTime, default=datetime.utcnow)
    lastUpdatedAt = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    #Added name and tenant_name as composite key
    __table_args__ = (
        db.PrimaryKeyConstraint('name', 'tenant_name'),
    )


# Represents metadata for a database within a Trino catalog.
# Each database is associated with a specific catalog.
class TrinoDBMetaData(db.Model):
    __tablename__ = 'trino_db_metadata'
    name = db.Column(db.String(255), nullable=False)
    trino_catalog_name = db.Column(db.String(255), nullable=False)
    tenant_name = db.Column(db.String(36), nullable=False)
    description = db.Column(db.Text)
    createdAt = db.Column(db.DateTime, default=datetime.utcnow)
    lastUpdatedAt = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    #Added name and tenant_name as composite key
    __table_args__ = (
        db.PrimaryKeyConstraint('name', 'tenant_name'),
        db.ForeignKeyConstraint(
            ['trino_catalog_name', 'tenant_name'],
            ['trino_catalog_metadata.name', 'trino_catalog_metadata.tenant_name'],
            ondelete="CASCADE"
        ),
    )


# Represents metadata for a table within a Trino database.
# Each table is associated with a specific database.
class TrinoTableMetaData(db.Model):
    __tablename__ = 'trino_table_metadata'
    name = db.Column(db.String(255), nullable=False)
    trino_db_name = db.Column(db.String(255), nullable=False)
    tenant_name = db.Column(db.String(36), nullable=False)
    description = db.Column(db.Text)
    createdAt = db.Column(db.DateTime, default=datetime.utcnow)
    lastUpdatedAt = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    #Added name and tenant_name as composite key
    __table_args__ = (
        db.PrimaryKeyConstraint('name', 'tenant_name'),
        db.ForeignKeyConstraint(
            ['trino_db_name', 'tenant_name'],
            ['trino_db_metadata.name', 'trino_db_metadata.tenant_name'],
            ondelete="CASCADE"
        ),
    )


# Represents metadata for a column within a Trino table.
# Includes column name, type, and an optional description.
class TrinoColumnMetaData(db.Model):
    __tablename__ = 'trino_column_metadata'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(255), nullable=False)
    type = db.Column(db.String(50), nullable=False)
    trino_table_name = db.Column(db.String(255), nullable=False)
    tenant_name = db.Column(db.String(36), nullable=False)
    description = db.Column(db.Text)
    createdAt = db.Column(db.DateTime, default=datetime.utcnow)
    lastUpdatedAt = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (
        db.ForeignKeyConstraint(
            ['trino_table_name', 'tenant_name'],
            ['trino_table_metadata.name', 'trino_table_metadata.tenant_name'],
            ondelete="CASCADE"
        ),
    )


# Represents tenant information, including a unique tenant ID and name.
class TenantInfo(db.Model):
    __tablename__ = 'tenant_info'
    tenant_name = db.Column(db.String(36), nullable=False)
    tenant_id = db.Column(db.String(36), nullable=False, primary_key=True)


# Represents entity metadata
class EntityMetaData(db.Model):
    __tablename__ = 'entity_metadata'
    entity_type = db.Column(db.String(36), primary_key=True)
    entity_id = db.Column(db.String(255))
    entity_name = db.Column(db.String(255))


## Schemas for serialization and validation

class TrinoCatalogMetaDataSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = TrinoCatalogMetaData
        load_instance = True

    tenant_name = fields.String(required=True)


class TrinoDBMetaDataSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = TrinoDBMetaData
        load_instance = True

    trino_catalog_name = fields.String(required=True)
    tenant_name = fields.String(required=True)


class TrinoTableMetaDataSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = TrinoTableMetaData
        load_instance = True

    trino_db_name = fields.String(required=True)
    tenant_name = fields.String(required=True)


class TrinoColumnMetaDataSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = TrinoColumnMetaData
        load_instance = True

    trino_table_name = fields.String(required=True)
    tenant_name = fields.String(required=True)


class TenantInfoSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = TenantInfo
        load_instance = True
