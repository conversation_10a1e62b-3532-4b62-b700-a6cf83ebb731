import base64
import json
from dataclasses import dataclass
from typing import Optional
from marshmallow import Schema, fields, post_load

@dataclass
class File:
    content: Optional[str] = None
    content_bytes: Optional[bytes] = None
    path: Optional[str] = None
    caption: Optional[str] = None

@dataclass
class Response:
    text: Optional[str] = None
    file: Optional[File] = None
    total_processing_time: Optional[float] = None
    llm_processing_time: Optional[float] = None


class FileSchema(Schema):
    path = fields.Str(allow_none=True)
    caption = fields.Str(allow_none=True)
    content_bytes = fields.Method(
        serialize="encode_content", deserialize="decode_content", allow_none=True
    )

    def encode_content(self, file):
        if file.content:
            return base64.b64encode(file.content_bytes).decode("utf-8")
        return None

    def decode_content(self, value):
        if value:
            return base64.b64decode(value)
        return None

    @post_load
    def make_file(self, data, **kwargs):
        return File(**data)


class ResponseSchema(Schema):
    text = fields.Str(required=True)
    file = fields.Nested(FileSchema, allow_none=True)
    total_processing_time = fields.Float()
    llm_processing_time = fields.Float()

    @post_load
    def make_response(self, data, **kwargs):
        # Ensure 'file' is set to None if it is not provided in the data
        if 'file' not in data:
            data['file'] = None
        return Response(**data)
