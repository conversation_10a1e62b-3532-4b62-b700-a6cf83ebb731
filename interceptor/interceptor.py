from flask import request
from dal import tenant_dal
from constants.app_constants import USER_ID_HEADER, TENANT_NAME_HEADER

from interceptor.thread_context import ThreadLocal<PERSON>ontext, thread_local
from utils.global_exception_handler import APIError

EXCLUDED_ENDPOINTS = ["/api/v1/metadata"]  #Define endpoints to exclude


def before_request():
    """
    This function runs before each request to capture the `User-Id` and `Tenant-Name`
    from the headers and store them in thread-local storage.
    """
    if request.path in EXCLUDED_ENDPOINTS:
        return  # Skip validation for specific endpoints

    user_id = request.headers.get(USER_ID_HEADER)
    tenant_name = request.headers.get(TENANT_NAME_HEADER)

    if not user_id or not tenant_name:
        raise APIError("User-Id and Tenant-Name headers are required.", 400)

    # Check if the tenant exists in the database
    tenant = tenant_dal.find_by_tenant_name(tenant_name)
    if not tenant:
        raise APIError(f"Tenant = {tenant_name} does not exist.", 404)

    ThreadLocalContext.set_user_and_tenant(user_id, tenant_name)


def after_request(response):
    """
    This function runs after each request to clear the thread-local storage.
    """
    thread_local.__dict__.clear()  # Clear thread-local storage after the request
    return response
