from config.lock_config import engine
from utils.global_exception_handler import APIError
from utils.LogUtils import LOG
from constants.app_constants import CHECK_LOCK_QUERY, RELEASE_LOCK_QUERY

'''
Referring 'ADVISORY LOCK' for PG database - https://www.postgresql.org/docs/current/explicit-locking.html#ADVISORY-LOCKS
Alternatively use 'NAMED LOCK' for MySQL database - https://dev.mysql.com/doc/refman/8.4/en/locking-functions.html
'''


def acquire_lock(lock_key: str):
    """Check and Acquire an advisory lock and return a persistent connection that keeps the lock active."""
    connection = engine.raw_connection()  # Get a raw connection
    try:
        with connection.cursor() as cursor:
            cursor.execute(CHECK_LOCK_QUERY, (lock_key,))
            result = cursor.fetchone()[0]  # Fetch lock status (True if acquired, False if already locked)

            if not result:
                connection.close()  # Ensure connection is closed if lock is not acquired
                LOG.error(f"Resource is locked for key: {lock_key}. Another process is currently using it.")
                raise APIError("The requested resource is currently in use. Please try again later.", 429)

            LOG.info(f"Lock acquired for {lock_key}")
        return connection  # Return connection if lock is successfully acquired
    except Exception as e:
        connection.close()
        raise e


def release_lock(connection, lock_key: str):
    """Release the advisory lock and close the connection."""
    try:
        with connection.cursor() as cursor:
            cursor.execute(RELEASE_LOCK_QUERY, (lock_key,))
            LOG.info(f"Released lock for key: {lock_key}")
    except Exception as e:
        LOG.error(f"Failed to release lock for key {lock_key}: {e}")
    finally:
        connection.close()  # Close the connection to fully release the lock
